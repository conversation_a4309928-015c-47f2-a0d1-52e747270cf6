import React, { createContext, useContext, useReducer, useEffect } from 'react'
import type { ReactNode } from 'react'
import type { Cart, CartItem, Product, ProductVariant } from '../types'
import { cartStorage } from '../utils/localStorage'

// Cart Actions
type CartAction =
  | { type: 'LOAD_CART'; payload: Cart }
  | { type: 'ADD_ITEM'; payload: { product: Product; variant: ProductVariant; quantity: number } }
  | { type: 'UPDATE_QUANTITY'; payload: { itemId: string; quantity: number } }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_CART' }

// Cart Context Type
interface CartContextType extends Cart {
  addItem: (product: Product, variant: ProductVariant, quantity?: number) => void
  updateQuantity: (itemId: string, quantity: number) => void
  removeItem: (itemId: string) => void
  clearCart: () => void
  getItemQuantity: (productId: string, variantId: string) => number
  isInCart: (productId: string, variantId: string) => boolean
}

// Initial State
const initialCart: Cart = {
  items: [],
  total: 0,
  itemCount: 0
}

// Calculate cart totals
const calculateTotals = (items: CartItem[]): { total: number; itemCount: number } => {
  const total = items.reduce((sum, item) => {
    const price = item.product.discountPrice || item.product.basePrice
    return sum + (price * item.quantity)
  }, 0)
  
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)
  
  return { total, itemCount }
}

// Cart Reducer
const cartReducer = (state: Cart, action: CartAction): Cart => {
  switch (action.type) {
    case 'LOAD_CART':
      return action.payload
      
    case 'ADD_ITEM': {
      const { product, variant, quantity } = action.payload
      const existingItemIndex = state.items.findIndex(
        item => item.productId === product.id && item.variantId === variant.id
      )

      let newItems: CartItem[]
      
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        newItems = state.items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + quantity }
            : item
        )
      } else {
        // Add new item
        const newItem: CartItem = {
          id: `${product.id}-${variant.id}`,
          productId: product.id,
          variantId: variant.id,
          quantity,
          product,
          variant
        }
        newItems = [...state.items, newItem]
      }

      const { total, itemCount } = calculateTotals(newItems)
      return { items: newItems, total, itemCount }
    }

    case 'UPDATE_QUANTITY': {
      const { itemId, quantity } = action.payload
      
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        const newItems = state.items.filter(item => item.id !== itemId)
        const { total, itemCount } = calculateTotals(newItems)
        return { items: newItems, total, itemCount }
      }

      const newItems = state.items.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
      
      const { total, itemCount } = calculateTotals(newItems)
      return { items: newItems, total, itemCount }
    }

    case 'REMOVE_ITEM': {
      const newItems = state.items.filter(item => item.id !== action.payload)
      const { total, itemCount } = calculateTotals(newItems)
      return { items: newItems, total, itemCount }
    }

    case 'CLEAR_CART':
      return initialCart

    default:
      return state
  }
}

// Create Context
const CartContext = createContext<CartContextType | undefined>(undefined)

// Cart Provider Component
export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [cart, dispatch] = useReducer(cartReducer, initialCart)

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = cartStorage.get() as Cart | null
    if (savedCart) {
      dispatch({ type: 'LOAD_CART', payload: savedCart })
    }
  }, [])

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    cartStorage.set(cart)
  }, [cart])

  // Add item to cart
  const addItem = (product: Product, variant: ProductVariant, quantity: number = 1): void => {
    // Check if variant has enough stock
    if (variant.stock < quantity) {
      throw new Error('الكمية المطلوبة غير متوفرة في المخزون')
    }

    dispatch({
      type: 'ADD_ITEM',
      payload: { product, variant, quantity }
    })
  }

  // Update item quantity
  const updateQuantity = (itemId: string, quantity: number): void => {
    const item = cart.items.find(item => item.id === itemId)
    
    if (item && quantity > item.variant.stock) {
      throw new Error('الكمية المطلوبة غير متوفرة في المخزون')
    }

    dispatch({
      type: 'UPDATE_QUANTITY',
      payload: { itemId, quantity }
    })
  }

  // Remove item from cart
  const removeItem = (itemId: string): void => {
    dispatch({ type: 'REMOVE_ITEM', payload: itemId })
  }

  // Clear entire cart
  const clearCart = (): void => {
    dispatch({ type: 'CLEAR_CART' })
  }

  // Get quantity of specific item in cart
  const getItemQuantity = (productId: string, variantId: string): number => {
    const item = cart.items.find(
      item => item.productId === productId && item.variantId === variantId
    )
    return item ? item.quantity : 0
  }

  // Check if item is in cart
  const isInCart = (productId: string, variantId: string): boolean => {
    return cart.items.some(
      item => item.productId === productId && item.variantId === variantId
    )
  }

  const value: CartContextType = {
    ...cart,
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    getItemQuantity,
    isInCart
  }

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

// Custom hook to use cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider')
  }
  return context
}
