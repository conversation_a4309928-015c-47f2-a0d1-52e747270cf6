import React from 'react'
import { Outlet } from 'react-router-dom'
import styled from 'styled-components'
import Header from './Header'
import Footer from './Footer'
import MobileBottomNav from '../UI/MobileBottomNav'

interface LayoutProps {
  isAdmin?: boolean
}

const LayoutContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colors.background};
`

const MainContent = styled.main`
  flex: 1;
  padding: ${({ theme }) => theme.spacing.lg} 0;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: ${({ theme }) => theme.spacing.md} 0;
    padding-bottom: 80px; /* Space for mobile bottom navigation */
  }
`

const Layout: React.FC<LayoutProps> = ({ isAdmin = false }) => {
  return (
    <LayoutContainer>
      <Header isAdmin={isAdmin} />
      <MainContent>
        <Outlet />
      </MainContent>
      {!isAdmin && <Footer />}
      {!isAdmin && <MobileBottomNav />}
    </LayoutContainer>
  )
}

export default Layout
