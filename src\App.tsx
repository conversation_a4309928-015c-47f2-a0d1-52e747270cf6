import React, { useEffect } from 'react'
import { BrowserRouter as Router } from 'react-router-dom'
import { ThemeProvider } from 'styled-components'
import { HelmetProvider } from 'react-helmet-async'
import { AuthProvider } from './context/AuthContext'
import { CartProvider } from './context/CartContext'
import { WishlistProvider } from './context/WishlistContext'
import { ProductProvider } from './context/ProductContext'
import { AdminProvider } from './context/AdminContext'
import AppRoutes from './routes/AppRoutes'
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary'
import GlobalStyles from './styles/GlobalStyles'
import { theme } from './styles/theme'
import { initPerformanceOptimizations } from './utils/performance'
import { initAccessibility } from './utils/accessibility'

const App: React.FC = () => {
  useEffect(() => {
    // Initialize performance optimizations
    initPerformanceOptimizations()

    // Initialize accessibility features
    initAccessibility()
  }, [])

  return (
    <HelmetProvider>
      <ErrorBoundary>
        <ThemeProvider theme={theme}>
          <GlobalStyles />
          <Router>
            <AuthProvider>
              <AdminProvider>
                <ProductProvider>
                  <CartProvider>
                    <WishlistProvider>
                      <AppRoutes />
                    </WishlistProvider>
                  </CartProvider>
                </ProductProvider>
              </AdminProvider>
            </AuthProvider>
          </Router>
        </ThemeProvider>
      </ErrorBoundary>
    </HelmetProvider>
  )
}

export default App
