import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import type { Product, Order, User } from '../types'

interface AdminContextType {
  // Products
  products: Product[]
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Product>
  updateProduct: (id: string, updates: Partial<Product>) => Promise<Product>
  deleteProduct: (id: string) => Promise<void>
  
  // Orders
  orders: Order[]
  updateOrderStatus: (orderId: string, status: Order['status']) => Promise<void>
  
  // Customers
  customers: User[]
  updateCustomer: (id: string, updates: Partial<User>) => Promise<User>
  
  // Statistics
  getStatistics: () => {
    totalRevenue: number
    totalOrders: number
    totalProducts: number
    totalCustomers: number
    recentOrders: Order[]
    topProducts: Array<{
      id: string
      name: string
      sales: number
      revenue: number
    }>
  }
  
  // Settings
  settings: {
    storeName: string
    currency: string
    taxRate: number
    shippingMethods: Array<{
      id: string
      name: string
      price: number
      estimatedDays: string
    }>
    paymentMethods: Array<{
      id: string
      name: string
      enabled: boolean
    }>
  }
  updateSettings: (updates: Partial<AdminContextType['settings']>) => Promise<void>
}

const AdminContext = createContext<AdminContextType | undefined>(undefined)

// Initial data
const initialProducts: Product[] = [
  {
    id: '1',
    name: 'قميص قطني أنيق',
    description: 'قميص قطني عالي الجودة مناسب للمناسبات الرسمية والكاجوال',
    price: 150,
    salePrice: 120,
    category: 'قمصان',
    images: ['/images/shirt1.jpg', '/images/shirt1-2.jpg'],
    stock: 25,
    sku: 'SH001',
    status: 'active',
    sizes: ['S', 'M', 'L', 'XL'],
    colors: ['أبيض', 'أزرق', 'أسود'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    name: 'فستان صيفي أنيق',
    description: 'فستان صيفي خفيف ومريح مناسب للطقس الحار',
    price: 280,
    category: 'فساتين',
    images: ['/images/dress1.jpg'],
    stock: 15,
    sku: 'DR001',
    status: 'active',
    sizes: ['S', 'M', 'L'],
    colors: ['وردي', 'أزرق فاتح', 'أبيض'],
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '3',
    name: 'بنطلون جينز كلاسيكي',
    description: 'بنطلون جينز عالي الجودة بقصة كلاسيكية مريحة',
    price: 200,
    salePrice: 170,
    category: 'بناطيل',
    images: ['/images/jeans1.jpg'],
    stock: 30,
    sku: 'JN001',
    status: 'active',
    sizes: ['28', '30', '32', '34', '36'],
    colors: ['أزرق داكن', 'أسود', 'رمادي'],
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25')
  }
]

const initialOrders: Order[] = [
  {
    id: 'ORD-001',
    customerId: 'cust-1',
    customerName: 'أحمد محمد',
    customerEmail: '<EMAIL>',
    customerPhone: '+966501234567',
    items: [
      {
        productId: '1',
        productName: 'قميص قطني أنيق',
        quantity: 2,
        price: 120,
        size: 'L',
        color: 'أبيض'
      }
    ],
    subtotal: 240,
    tax: 36,
    shipping: 25,
    total: 301,
    status: 'pending',
    paymentMethod: 'credit_card',
    paymentStatus: 'paid',
    shippingAddress: {
      street: 'شارع الملك فهد',
      city: 'الرياض',
      state: 'الرياض',
      zipCode: '12345',
      country: 'السعودية'
    },
    createdAt: new Date('2024-01-28'),
    updatedAt: new Date('2024-01-28')
  },
  {
    id: 'ORD-002',
    customerId: 'cust-2',
    customerName: 'فاطمة علي',
    customerEmail: '<EMAIL>',
    customerPhone: '+966507654321',
    items: [
      {
        productId: '2',
        productName: 'فستان صيفي أنيق',
        quantity: 1,
        price: 280,
        size: 'M',
        color: 'وردي'
      }
    ],
    subtotal: 280,
    tax: 42,
    shipping: 25,
    total: 347,
    status: 'confirmed',
    paymentMethod: 'cash_on_delivery',
    paymentStatus: 'pending',
    shippingAddress: {
      street: 'شارع العليا',
      city: 'جدة',
      state: 'مكة المكرمة',
      zipCode: '23456',
      country: 'السعودية'
    },
    createdAt: new Date('2024-01-29'),
    updatedAt: new Date('2024-01-29')
  }
]

const initialCustomers: User[] = [
  {
    id: 'cust-1',
    name: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+966501234567',
    role: 'customer',
    address: {
      street: 'شارع الملك فهد',
      city: 'الرياض',
      state: 'الرياض',
      zipCode: '12345',
      country: 'السعودية'
    },
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'cust-2',
    name: 'فاطمة علي',
    email: '<EMAIL>',
    phone: '+966507654321',
    role: 'customer',
    address: {
      street: 'شارع العليا',
      city: 'جدة',
      state: 'مكة المكرمة',
      zipCode: '23456',
      country: 'السعودية'
    },
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20')
  }
]

const initialSettings = {
  storeName: 'متجر الملابس العربي',
  currency: 'SAR',
  taxRate: 0.15,
  shippingMethods: [
    { id: '1', name: 'توصيل عادي', price: 25, estimatedDays: '3-5 أيام' },
    { id: '2', name: 'توصيل سريع', price: 50, estimatedDays: '1-2 أيام' },
    { id: '3', name: 'توصيل فوري', price: 100, estimatedDays: 'نفس اليوم' }
  ],
  paymentMethods: [
    { id: '1', name: 'بطاقة ائتمان', enabled: true },
    { id: '2', name: 'الدفع عند الاستلام', enabled: true },
    { id: '3', name: 'تحويل بنكي', enabled: false },
    { id: '4', name: 'محفظة رقمية', enabled: true }
  ]
}

export const AdminProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [products, setProducts] = useState<Product[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [customers, setCustomers] = useState<User[]>([])
  const [settings, setSettings] = useState(initialSettings)

  // Load data from localStorage on mount
  useEffect(() => {
    const savedProducts = localStorage.getItem('admin_products')
    const savedOrders = localStorage.getItem('admin_orders')
    const savedCustomers = localStorage.getItem('admin_customers')
    const savedSettings = localStorage.getItem('admin_settings')

    setProducts(savedProducts ? JSON.parse(savedProducts) : initialProducts)
    setOrders(savedOrders ? JSON.parse(savedOrders) : initialOrders)
    setCustomers(savedCustomers ? JSON.parse(savedCustomers) : initialCustomers)
    setSettings(savedSettings ? JSON.parse(savedSettings) : initialSettings)
  }, [])

  // Save to localStorage whenever data changes
  useEffect(() => {
    localStorage.setItem('admin_products', JSON.stringify(products))
  }, [products])

  useEffect(() => {
    localStorage.setItem('admin_orders', JSON.stringify(orders))
  }, [orders])

  useEffect(() => {
    localStorage.setItem('admin_customers', JSON.stringify(customers))
  }, [customers])

  useEffect(() => {
    localStorage.setItem('admin_settings', JSON.stringify(settings))
  }, [settings])

  // Product management
  const addProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
    const newProduct: Product = {
      ...productData,
      id: `prod-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    setProducts(prev => [...prev, newProduct])
    return newProduct
  }

  const updateProduct = async (id: string, updates: Partial<Product>): Promise<Product> => {
    const updatedProduct = { ...updates, id, updatedAt: new Date() } as Product
    
    setProducts(prev => prev.map(product => 
      product.id === id ? { ...product, ...updatedProduct } : product
    ))
    
    return updatedProduct
  }

  const deleteProduct = async (id: string): Promise<void> => {
    setProducts(prev => prev.filter(product => product.id !== id))
  }

  // Order management
  const updateOrderStatus = async (orderId: string, status: Order['status']): Promise<void> => {
    setOrders(prev => prev.map(order => 
      order.id === orderId ? { ...order, status, updatedAt: new Date() } : order
    ))
  }

  // Customer management
  const updateCustomer = async (id: string, updates: Partial<User>): Promise<User> => {
    const updatedCustomer = { ...updates, id, updatedAt: new Date() } as User
    
    setCustomers(prev => prev.map(customer => 
      customer.id === id ? { ...customer, ...updatedCustomer } : customer
    ))
    
    return updatedCustomer
  }

  // Statistics
  const getStatistics = () => {
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0)
    const totalOrders = orders.length
    const totalProducts = products.length
    const totalCustomers = customers.length
    
    const recentOrders = orders
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
    
    const productSales = orders.flatMap(order => order.items)
      .reduce((acc, item) => {
        const existing = acc.find(p => p.id === item.productId)
        if (existing) {
          existing.sales += item.quantity
          existing.revenue += item.price * item.quantity
        } else {
          acc.push({
            id: item.productId,
            name: item.productName,
            sales: item.quantity,
            revenue: item.price * item.quantity
          })
        }
        return acc
      }, [] as Array<{ id: string; name: string; sales: number; revenue: number }>)
    
    const topProducts = productSales
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5)

    return {
      totalRevenue,
      totalOrders,
      totalProducts,
      totalCustomers,
      recentOrders,
      topProducts
    }
  }

  // Settings management
  const updateSettings = async (updates: Partial<AdminContextType['settings']>): Promise<void> => {
    setSettings(prev => ({ ...prev, ...updates }))
  }

  const value: AdminContextType = {
    products,
    addProduct,
    updateProduct,
    deleteProduct,
    orders,
    updateOrderStatus,
    customers,
    updateCustomer,
    getStatistics,
    settings,
    updateSettings
  }

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  )
}

export const useAdmin = () => {
  const context = useContext(AdminContext)
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider')
  }
  return context
}

export default AdminContext
