import React, { createContext, useContext, useReducer, useEffect } from 'react'
import type { ReactNode } from 'react'
import type { Wishlist, WishlistItem, Product } from '../types'
import { wishlistStorage } from '../utils/localStorage'

// Wishlist Actions
type WishlistAction =
  | { type: 'LOAD_WISHLIST'; payload: Wishlist }
  | { type: 'ADD_ITEM'; payload: Product }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_WISHLIST' }

// Wishlist Context Type
interface WishlistContextType extends Wishlist {
  addItem: (product: Product) => void
  removeItem: (productId: string) => void
  clearWishlist: () => void
  isInWishlist: (productId: string) => boolean
  toggleItem: (product: Product) => void
}

// Initial State
const initialWishlist: Wishlist = {
  items: [],
  itemCount: 0
}

// Wishlist Reducer
const wishlistReducer = (state: Wishlist, action: WishlistAction): Wishlist => {
  switch (action.type) {
    case 'LOAD_WISHLIST':
      return action.payload

    case 'ADD_ITEM': {
      const product = action.payload
      
      // Check if item already exists
      const existingItem = state.items.find(item => item.productId === product.id)
      if (existingItem) {
        return state // Item already in wishlist
      }

      const newItem: WishlistItem = {
        id: `wishlist-${product.id}`,
        productId: product.id,
        product,
        addedAt: new Date()
      }

      return {
        items: [...state.items, newItem],
        itemCount: state.itemCount + 1
      }
    }

    case 'REMOVE_ITEM': {
      const productId = action.payload
      const newItems = state.items.filter(item => item.productId !== productId)
      
      return {
        items: newItems,
        itemCount: newItems.length
      }
    }

    case 'CLEAR_WISHLIST':
      return initialWishlist

    default:
      return state
  }
}

// Create Context
const WishlistContext = createContext<WishlistContextType | undefined>(undefined)

// Wishlist Provider Component
export const WishlistProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [wishlist, dispatch] = useReducer(wishlistReducer, initialWishlist)

  // Load wishlist from localStorage on mount
  useEffect(() => {
    const savedWishlist = wishlistStorage.get() as Wishlist | null
    if (savedWishlist) {
      dispatch({ type: 'LOAD_WISHLIST', payload: savedWishlist })
    }
  }, [])

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    wishlistStorage.set(wishlist)
  }, [wishlist])

  // Add item to wishlist
  const addItem = (product: Product): void => {
    dispatch({ type: 'ADD_ITEM', payload: product })
  }

  // Remove item from wishlist
  const removeItem = (productId: string): void => {
    dispatch({ type: 'REMOVE_ITEM', payload: productId })
  }

  // Clear entire wishlist
  const clearWishlist = (): void => {
    dispatch({ type: 'CLEAR_WISHLIST' })
  }

  // Check if item is in wishlist
  const isInWishlist = (productId: string): boolean => {
    return wishlist.items.some(item => item.productId === productId)
  }

  // Toggle item in wishlist (add if not present, remove if present)
  const toggleItem = (product: Product): void => {
    if (isInWishlist(product.id)) {
      removeItem(product.id)
    } else {
      addItem(product)
    }
  }

  const value: WishlistContextType = {
    ...wishlist,
    addItem,
    removeItem,
    clearWishlist,
    isInWishlist,
    toggleItem
  }

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  )
}

// Custom hook to use wishlist context
export const useWishlist = (): WishlistContextType => {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider')
  }
  return context
}
