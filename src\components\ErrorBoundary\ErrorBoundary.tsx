import React, { Component, ErrorInfo, ReactNode } from 'react'
import styled from 'styled-components'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'
import Button from '../UI/Button'
import Card from '../UI/Card'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

const ErrorContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: ${({ theme }) => theme?.spacing?.xl || '2rem'};
`

const ErrorCard = styled(Card)`
  max-width: 500px;
  text-align: center;
`

const ErrorIcon = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: ${({ theme }) => theme?.spacing?.lg || '1.5rem'};
  color: ${({ theme }) => theme?.colors?.status?.error || '#dc2626'};
`

const ErrorTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme?.fonts?.weights?.bold || '700'};
  color: ${({ theme }) => theme?.colors?.text?.primary || '#000'};
  margin-bottom: ${({ theme }) => theme?.spacing?.md || '1rem'};
`

const ErrorMessage = styled.p`
  color: ${({ theme }) => theme?.colors?.text?.secondary || '#666'};
  margin-bottom: ${({ theme }) => theme?.spacing?.lg || '1.5rem'};
  line-height: 1.6;
`

const ErrorActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme?.spacing?.md || '1rem'};
  justify-content: center;
  flex-wrap: wrap;
`

const ErrorDetails = styled.details`
  margin-top: ${({ theme }) => theme?.spacing?.lg || '1.5rem'};
  text-align: right;

  summary {
    cursor: pointer;
    color: ${({ theme }) => theme?.colors?.text?.secondary || '#666'};
    font-size: 0.875rem;
    margin-bottom: ${({ theme }) => theme?.spacing?.sm || '0.5rem'};
  }

  pre {
    background-color: ${({ theme }) => theme?.colors?.surface || '#f5f5f5'};
    padding: ${({ theme }) => theme?.spacing?.md || '1rem'};
    border-radius: ${({ theme }) => theme?.borderRadius?.md || '8px'};
    font-size: 0.75rem;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
    direction: ltr;
    text-align: left;
  }
`

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // In production, you might want to log this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo)
    }
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <ErrorContainer>
          <ErrorCard padding="xl">
            <ErrorIcon>
              <AlertTriangle size={48} />
            </ErrorIcon>
            
            <ErrorTitle>عذراً، حدث خطأ غير متوقع</ErrorTitle>
            
            <ErrorMessage>
              نعتذر عن هذا الإزعاج. حدث خطأ في التطبيق ولم نتمكن من عرض هذه الصفحة.
              يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.
            </ErrorMessage>
            
            <ErrorActions>
              <Button 
                variant="primary" 
                onClick={this.handleReload}
                icon={<RefreshCw size={16} />}
              >
                إعادة تحميل الصفحة
              </Button>
              
              <Button 
                variant="outline" 
                onClick={this.handleGoHome}
                icon={<Home size={16} />}
              >
                العودة للرئيسية
              </Button>
            </ErrorActions>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <ErrorDetails>
                <summary>تفاصيل الخطأ (للمطورين)</summary>
                <pre>
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </ErrorDetails>
            )}
          </ErrorCard>
        </ErrorContainer>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
