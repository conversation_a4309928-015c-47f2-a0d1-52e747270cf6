import React, { createContext, useContext, useReducer, useEffect } from 'react'
import type { ReactNode } from 'react'
import type { Product, ProductFilter, Category, LoadingState } from '../types'

// Product Actions
type ProductAction =
  | { type: 'SET_LOADING'; payload: LoadingState }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'ADD_PRODUCT'; payload: Product }
  | { type: 'UPDATE_PRODUCT'; payload: Product }
  | { type: 'DELETE_PRODUCT'; payload: string }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'ADD_CATEGORY'; payload: Category }
  | { type: 'UPDATE_CATEGORY'; payload: Category }
  | { type: 'DELETE_CATEGORY'; payload: string }
  | { type: 'SET_FILTER'; payload: ProductFilter }
  | { type: 'CLEAR_FILTER' }

// Product State
interface ProductState {
  products: Product[]
  categories: Category[]
  filteredProducts: Product[]
  currentFilter: ProductFilter
  loading: LoadingState
  error: string | null
}

// Product Context Type
interface ProductContextType extends ProductState {
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateProduct: (id: string, updates: Partial<Product>) => void
  deleteProduct: (id: string) => void
  getProduct: (id: string) => Product | undefined
  addCategory: (category: Omit<Category, 'id'>) => void
  updateCategory: (id: string, updates: Partial<Category>) => void
  deleteCategory: (id: string) => void
  setFilter: (filter: ProductFilter) => void
  clearFilter: () => void
  searchProducts: (query: string) => Product[]
}

// Initial State
const initialState: ProductState = {
  products: [],
  categories: [],
  filteredProducts: [],
  currentFilter: {},
  loading: 'idle',
  error: null
}

// Product Reducer
const productReducer = (state: ProductState, action: ProductAction): ProductState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      }
    case 'SET_PRODUCTS':
      return {
        ...state,
        products: action.payload,
        filteredProducts: applyFilter(action.payload, state.currentFilter),
        loading: 'success'
      }
    case 'ADD_PRODUCT':
      const newProducts = [...state.products, action.payload]
      return {
        ...state,
        products: newProducts,
        filteredProducts: applyFilter(newProducts, state.currentFilter)
      }
    case 'UPDATE_PRODUCT':
      const updatedProducts = state.products.map(product =>
        product.id === action.payload.id ? action.payload : product
      )
      return {
        ...state,
        products: updatedProducts,
        filteredProducts: applyFilter(updatedProducts, state.currentFilter)
      }
    case 'DELETE_PRODUCT':
      const remainingProducts = state.products.filter(product => product.id !== action.payload)
      return {
        ...state,
        products: remainingProducts,
        filteredProducts: applyFilter(remainingProducts, state.currentFilter)
      }
    case 'SET_CATEGORIES':
      return {
        ...state,
        categories: action.payload
      }
    case 'ADD_CATEGORY':
      return {
        ...state,
        categories: [...state.categories, action.payload]
      }
    case 'UPDATE_CATEGORY':
      return {
        ...state,
        categories: state.categories.map(category =>
          category.id === action.payload.id ? action.payload : category
        )
      }
    case 'DELETE_CATEGORY':
      return {
        ...state,
        categories: state.categories.filter(category => category.id !== action.payload)
      }
    case 'SET_FILTER':
      return {
        ...state,
        currentFilter: action.payload,
        filteredProducts: applyFilter(state.products, action.payload)
      }
    case 'CLEAR_FILTER':
      return {
        ...state,
        currentFilter: {},
        filteredProducts: state.products
      }
    default:
      return state
  }
}

// Filter application function
const applyFilter = (products: Product[], filter: ProductFilter): Product[] => {
  let filtered = [...products]

  if (filter.category) {
    filtered = filtered.filter(product => product.category === filter.category)
  }

  if (filter.subcategory) {
    filtered = filtered.filter(product => product.subcategory === filter.subcategory)
  }

  if (filter.minPrice !== undefined) {
    filtered = filtered.filter(product => product.basePrice >= filter.minPrice!)
  }

  if (filter.maxPrice !== undefined) {
    filtered = filtered.filter(product => product.basePrice <= filter.maxPrice!)
  }

  if (filter.sizes && filter.sizes.length > 0) {
    filtered = filtered.filter(product =>
      product.variants.some(variant => filter.sizes!.includes(variant.size))
    )
  }

  if (filter.colors && filter.colors.length > 0) {
    filtered = filtered.filter(product =>
      product.variants.some(variant => filter.colors!.includes(variant.color))
    )
  }

  if (filter.tags && filter.tags.length > 0) {
    filtered = filtered.filter(product =>
      filter.tags!.some(tag => product.tags.includes(tag))
    )
  }

  if (filter.isOnSale !== undefined) {
    filtered = filtered.filter(product => product.isOnSale === filter.isOnSale)
  }

  // Apply sorting
  if (filter.sortBy) {
    filtered.sort((a, b) => {
      let comparison = 0
      
      switch (filter.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name, 'ar')
          break
        case 'price':
          comparison = a.basePrice - b.basePrice
          break
        case 'rating':
          comparison = a.rating - b.rating
          break
        case 'newest':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          break
        default:
          return 0
      }

      return filter.sortOrder === 'desc' ? -comparison : comparison
    })
  }

  return filtered
}

// Create Context
const ProductContext = createContext<ProductContextType | undefined>(undefined)

// Product Provider Component
export const ProductProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(productReducer, initialState)

  // Initialize with default categories
  useEffect(() => {
    const defaultCategories: Category[] = [
      { id: '1', name: 'قمصان', slug: 'shirts', isActive: true, sortOrder: 1 },
      { id: '2', name: 'بناطيل', slug: 'pants', isActive: true, sortOrder: 2 },
      { id: '3', name: 'فساتين', slug: 'dresses', isActive: true, sortOrder: 3 },
      { id: '4', name: 'جاكيتات', slug: 'jackets', isActive: true, sortOrder: 4 },
      { id: '5', name: 'أحذية', slug: 'shoes', isActive: true, sortOrder: 5 },
      { id: '6', name: 'إكسسوارات', slug: 'accessories', isActive: true, sortOrder: 6 }
    ]
    
    dispatch({ type: 'SET_CATEGORIES', payload: defaultCategories })
  }, [])

  // Add product function
  const addProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): void => {
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    dispatch({ type: 'ADD_PRODUCT', payload: newProduct })
  }

  // Update product function
  const updateProduct = (id: string, updates: Partial<Product>): void => {
    const existingProduct = state.products.find(p => p.id === id)
    if (existingProduct) {
      const updatedProduct: Product = {
        ...existingProduct,
        ...updates,
        updatedAt: new Date()
      }
      dispatch({ type: 'UPDATE_PRODUCT', payload: updatedProduct })
    }
  }

  // Delete product function
  const deleteProduct = (id: string): void => {
    dispatch({ type: 'DELETE_PRODUCT', payload: id })
  }

  // Get product function
  const getProduct = (id: string): Product | undefined => {
    return state.products.find(product => product.id === id)
  }

  // Add category function
  const addCategory = (categoryData: Omit<Category, 'id'>): void => {
    const newCategory: Category = {
      ...categoryData,
      id: Date.now().toString()
    }
    dispatch({ type: 'ADD_CATEGORY', payload: newCategory })
  }

  // Update category function
  const updateCategory = (id: string, updates: Partial<Category>): void => {
    const existingCategory = state.categories.find(c => c.id === id)
    if (existingCategory) {
      const updatedCategory: Category = {
        ...existingCategory,
        ...updates
      }
      dispatch({ type: 'UPDATE_CATEGORY', payload: updatedCategory })
    }
  }

  // Delete category function
  const deleteCategory = (id: string): void => {
    dispatch({ type: 'DELETE_CATEGORY', payload: id })
  }

  // Set filter function
  const setFilter = (filter: ProductFilter): void => {
    dispatch({ type: 'SET_FILTER', payload: filter })
  }

  // Clear filter function
  const clearFilter = (): void => {
    dispatch({ type: 'CLEAR_FILTER' })
  }

  // Search products function
  const searchProducts = (query: string): Product[] => {
    const searchTerm = query.toLowerCase().trim()
    if (!searchTerm) return state.products

    return state.products.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
      product.category.toLowerCase().includes(searchTerm)
    )
  }

  const value: ProductContextType = {
    ...state,
    addProduct,
    updateProduct,
    deleteProduct,
    getProduct,
    addCategory,
    updateCategory,
    deleteCategory,
    setFilter,
    clearFilter,
    searchProducts
  }

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  )
}

// Custom hook to use product context
export const useProducts = (): ProductContextType => {
  const context = useContext(ProductContext)
  if (context === undefined) {
    throw new Error('useProducts must be used within a ProductProvider')
  }
  return context
}
