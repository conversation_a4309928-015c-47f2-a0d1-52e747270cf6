{"name": "arabic-clothing-store", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"date-fns": "^4.1.0", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.2", "react-router-dom": "^6.28.0", "styled-components": "^6.1.13"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-helmet-async": "^1.0.1", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "typescript": "~5.8.3", "vite": "^7.0.4"}}