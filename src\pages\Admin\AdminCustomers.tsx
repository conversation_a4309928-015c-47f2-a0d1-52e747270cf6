import React, { useState } from 'react'
import styled from 'styled-components'
import {
  Search,
  Filter,
  Eye,
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingBag,
  DollarSign,
  Users,
  UserPlus,
  Download
} from 'lucide-react'
import { formatPrice, formatDate, formatPhoneNumber } from '../../utils/formatters'
import Card from '../../components/UI/Card'
import Button from '../../components/UI/Button'
import Input from '../../components/UI/Input'
import Select from '../../components/UI/Select'
import Badge from '../../components/UI/Badge'
import Modal from '../../components/UI/Modal'

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    justify-content: stretch;
  }
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const StatCard = styled(Card)`
  text-align: center;
`

const StatIcon = styled.div<{ $color: string }>`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ $color }) => $color}20;
  color: ${({ $color }) => $color};
  margin: 0 auto ${({ theme }) => theme.spacing.md};
`

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const FiltersSection = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: end;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`

const CustomersTable = styled(Card)`
  overflow: hidden;
`

const TableHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const TableTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const Table = styled.div`
  overflow-x: auto;
`

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 120px;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  align-items: center;

  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }

  &:first-child {
    background-color: ${({ theme }) => theme.colors.surface};
    font-weight: ${({ theme }) => theme.fonts.weights.semibold};
    border-bottom: 2px solid ${({ theme }) => theme.colors.border.medium};

    &:hover {
      background-color: ${({ theme }) => theme.colors.surface};
    }
  }
`

const CustomerInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const CustomerName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const CustomerEmail = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
`

const CustomerPhone = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
`

const CustomerActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  justify-content: flex-end;
`

const ActionButton = styled(Button)`
  padding: ${({ theme }) => theme.spacing.xs};
  min-width: auto;
`

// Mock data
const mockCustomerStats = {
  totalCustomers: 892,
  newCustomers: 45,
  activeCustomers: 756,
  vipCustomers: 23
}

const mockCustomers = [
  {
    id: '1',
    name: 'أحمد محمد علي',
    email: '<EMAIL>',
    phone: '+966501234567',
    joinDate: '2023-12-15',
    totalOrders: 12,
    totalSpent: 2450.00,
    status: 'active',
    city: 'الرياض'
  },
  {
    id: '2',
    name: 'فاطمة علي أحمد',
    email: '<EMAIL>',
    phone: '+966507654321',
    joinDate: '2023-11-20',
    totalOrders: 8,
    totalSpent: 1890.50,
    status: 'active',
    city: 'جدة'
  },
  {
    id: '3',
    name: 'محمد سالم خالد',
    email: '<EMAIL>',
    phone: '+966509876543',
    joinDate: '2023-10-05',
    totalOrders: 25,
    totalSpent: 5670.00,
    status: 'vip',
    city: 'الدمام'
  },
  {
    id: '4',
    name: 'نورا أحمد محمد',
    email: '<EMAIL>',
    phone: '+966502468135',
    joinDate: '2024-01-10',
    totalOrders: 3,
    totalSpent: 450.00,
    status: 'new',
    city: 'الرياض'
  },
  {
    id: '5',
    name: 'خالد عبدالله سعد',
    email: '<EMAIL>',
    phone: '+966508642097',
    joinDate: '2023-09-12',
    totalOrders: 0,
    totalSpent: 0,
    status: 'inactive',
    city: 'مكة'
  }
]

const AdminCustomers: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [cityFilter, setCityFilter] = useState('')
  const [showCustomerModal, setShowCustomerModal] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success" size="sm">نشط</Badge>
      case 'inactive':
        return <Badge variant="secondary" size="sm">غير نشط</Badge>
      case 'new':
        return <Badge variant="info" size="sm">جديد</Badge>
      case 'vip':
        return <Badge variant="primary" size="sm">VIP</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  const filteredCustomers = mockCustomers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm)
    const matchesStatus = !statusFilter || customer.status === statusFilter
    const matchesCity = !cityFilter || customer.city === cityFilter

    return matchesSearch && matchesStatus && matchesCity
  })

  const handleViewCustomer = (customer: any) => {
    setSelectedCustomer(customer)
    setShowCustomerModal(true)
  }

  return (
    <Container>
      <Header>
        <Title>إدارة العملاء</Title>
        <HeaderActions>
          <Button variant="outline">
            <Download size={16} />
            تصدير
          </Button>
          <Button variant="primary">
            <UserPlus size={16} />
            إضافة عميل
          </Button>
        </HeaderActions>
      </Header>

      <StatsGrid>
        <StatCard padding="lg">
          <StatIcon $color="#3B82F6">
            <Users size={24} />
          </StatIcon>
          <StatValue>{mockCustomerStats.totalCustomers}</StatValue>
          <StatLabel>إجمالي العملاء</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#10B981">
            <UserPlus size={24} />
          </StatIcon>
          <StatValue>{mockCustomerStats.newCustomers}</StatValue>
          <StatLabel>عملاء جدد</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#F59E0B">
            <ShoppingBag size={24} />
          </StatIcon>
          <StatValue>{mockCustomerStats.activeCustomers}</StatValue>
          <StatLabel>عملاء نشطون</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#8B5CF6">
            <DollarSign size={24} />
          </StatIcon>
          <StatValue>{mockCustomerStats.vipCustomers}</StatValue>
          <StatLabel>عملاء VIP</StatLabel>
        </StatCard>
      </StatsGrid>

      <FiltersSection padding="lg">
        <FiltersGrid>
          <Input
            type="text"
            placeholder="البحث في العملاء..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<Search size={16} />}
          />

          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع الحالات' },
              { value: 'active', label: 'نشط' },
              { value: 'inactive', label: 'غير نشط' },
              { value: 'new', label: 'جديد' },
              { value: 'vip', label: 'VIP' }
            ]}
          />

          <Select
            value={cityFilter}
            onChange={(e) => setCityFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع المدن' },
              { value: 'الرياض', label: 'الرياض' },
              { value: 'جدة', label: 'جدة' },
              { value: 'الدمام', label: 'الدمام' },
              { value: 'مكة', label: 'مكة' },
              { value: 'المدينة', label: 'المدينة' }
            ]}
          />

          <Button variant="outline">
            <Filter size={16} />
            تصفية متقدمة
          </Button>
        </FiltersGrid>
      </FiltersSection>

      <CustomersTable padding="lg">
        <TableHeader>
          <TableTitle>العملاء ({filteredCustomers.length})</TableTitle>
          <Button variant="outline" size="sm">
            تصدير البيانات
          </Button>
        </TableHeader>

        <Table>
          <TableRow>
            <div>العميل</div>
            <div>المدينة</div>
            <div>تاريخ الانضمام</div>
            <div>الطلبات</div>
            <div>الحالة</div>
            <div>الإجراءات</div>
          </TableRow>

          {filteredCustomers.map((customer) => (
            <TableRow key={customer.id}>
              <CustomerInfo>
                <CustomerName>{customer.name}</CustomerName>
                <CustomerEmail>
                  <Mail size={12} />
                  {customer.email}
                </CustomerEmail>
                <CustomerPhone>
                  <Phone size={12} />
                  {formatPhoneNumber(customer.phone)}
                </CustomerPhone>
              </CustomerInfo>

              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <MapPin size={12} />
                {customer.city}
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <Calendar size={12} />
                {formatDate(customer.joinDate)}
              </div>

              <div>
                <div style={{ fontWeight: 'bold' }}>{customer.totalOrders} طلب</div>
                <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>
                  {formatPrice(customer.totalSpent)} ر.س
                </div>
              </div>

              <div>{getStatusBadge(customer.status)}</div>

              <CustomerActions>
                <ActionButton variant="outline" size="sm" onClick={() => handleViewCustomer(customer)}>
                  <Eye size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Edit size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Mail size={14} />
                </ActionButton>
              </CustomerActions>
            </TableRow>
          ))}
        </Table>
      </CustomersTable>

      {showCustomerModal && selectedCustomer && (
        <Modal
          isOpen={showCustomerModal}
          onClose={() => setShowCustomerModal(false)}
          title={`تفاصيل العميل: ${selectedCustomer.name}`}
        >
          <div style={{ padding: '1rem' }}>
            <div style={{ marginBottom: '1rem' }}>
              <strong>الاسم:</strong> {selectedCustomer.name}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>البريد الإلكتروني:</strong> {selectedCustomer.email}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>رقم الهاتف:</strong> {formatPhoneNumber(selectedCustomer.phone)}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>المدينة:</strong> {selectedCustomer.city}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>تاريخ الانضمام:</strong> {formatDate(selectedCustomer.joinDate)}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>عدد الطلبات:</strong> {selectedCustomer.totalOrders}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>إجمالي المشتريات:</strong> {formatPrice(selectedCustomer.totalSpent)} ر.س
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>الحالة:</strong> {getStatusBadge(selectedCustomer.status)}
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', marginTop: '2rem' }}>
              <Button variant="outline" onClick={() => setShowCustomerModal(false)}>
                إغلاق
              </Button>
              <Button variant="outline">
                <Mail size={16} />
                إرسال رسالة
              </Button>
              <Button variant="primary">
                تعديل البيانات
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </Container>
  )
}

export default AdminCustomers
