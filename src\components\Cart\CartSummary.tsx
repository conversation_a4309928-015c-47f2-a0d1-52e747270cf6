import React from 'react'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { useCart } from '../../context/CartContext'
import { formatPrice } from '../../utils/formatters'
import Button from '../UI/Button'
import Card from '../UI/Card'

const SummaryCard = styled(Card)`
  position: sticky;
  top: ${({ theme }) => theme.spacing.lg};
`

const SummaryTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
  text-align: center;
`

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.sm} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  
  &:last-of-type {
    border-bottom: none;
    padding-bottom: 0;
  }
`

const SummaryLabel = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const SummaryValue = styled.span`
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
`

const TotalRow = styled(SummaryRow)`
  margin-top: ${({ theme }) => theme.spacing.md};
  padding-top: ${({ theme }) => theme.spacing.md};
  border-top: 2px solid ${({ theme }) => theme.colors.border.medium};
  border-bottom: none;
`

const TotalLabel = styled.span`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const TotalValue = styled.span`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.primary};
`

const CheckoutButton = styled(Button)`
  width: 100%;
  margin-top: ${({ theme }) => theme.spacing.lg};
`

const ContinueShoppingButton = styled(Button)`
  width: 100%;
  margin-top: ${({ theme }) => theme.spacing.sm};
`

const EmptyCartMessage = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  padding: ${({ theme }) => theme.spacing.xl};
`

const ItemCount = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const CartSummary: React.FC = () => {
  const { items, total, itemCount, clearCart } = useCart()
  const navigate = useNavigate()

  const subtotal = total
  const shipping = subtotal > 200 ? 0 : 25 // Free shipping over 200 SAR
  const tax = subtotal * 0.15 // 15% VAT
  const total = subtotal + shipping + tax

  const handleCheckout = () => {
    navigate('/checkout')
  }

  const handleContinueShopping = () => {
    navigate('/products')
  }

  if (items.length === 0) {
    return (
      <SummaryCard>
        <EmptyCartMessage>
          <p>سلة التسوق فارغة</p>
          <ContinueShoppingButton
            variant="primary"
            onClick={handleContinueShopping}
          >
            تصفح المنتجات
          </ContinueShoppingButton>
        </EmptyCartMessage>
      </SummaryCard>
    )
  }

  return (
    <SummaryCard>
      <SummaryTitle>ملخص الطلب</SummaryTitle>
      
      <ItemCount>
        {itemCount} {itemCount === 1 ? 'منتج' : 'منتجات'}
      </ItemCount>
      
      <SummaryRow>
        <SummaryLabel>المجموع الفرعي</SummaryLabel>
        <SummaryValue>{formatPrice(subtotal)}</SummaryValue>
      </SummaryRow>
      
      <SummaryRow>
        <SummaryLabel>
          الشحن
          {shipping === 0 && (
            <span style={{ color: '#10b981', fontSize: '0.75rem', marginRight: '4px' }}>
              (مجاني)
            </span>
          )}
        </SummaryLabel>
        <SummaryValue>{formatPrice(shipping)}</SummaryValue>
      </SummaryRow>
      
      <SummaryRow>
        <SummaryLabel>ضريبة القيمة المضافة (15%)</SummaryLabel>
        <SummaryValue>{formatPrice(tax)}</SummaryValue>
      </SummaryRow>
      
      <TotalRow>
        <TotalLabel>المجموع الكلي</TotalLabel>
        <TotalValue>{formatPrice(total)}</TotalValue>
      </TotalRow>
      
      <CheckoutButton
        variant="primary"
        size="lg"
        onClick={handleCheckout}
      >
        متابعة إلى الدفع
      </CheckoutButton>
      
      <ContinueShoppingButton
        variant="outline"
        onClick={handleContinueShopping}
      >
        متابعة التسوق
      </ContinueShoppingButton>
      
      {subtotal < 200 && (
        <div style={{ 
          fontSize: '0.75rem', 
          color: '#10b981', 
          textAlign: 'center', 
          marginTop: '8px' 
        }}>
          أضف {formatPrice(200 - subtotal)} للحصول على شحن مجاني
        </div>
      )}
    </SummaryCard>
  )
}

export default CartSummary
