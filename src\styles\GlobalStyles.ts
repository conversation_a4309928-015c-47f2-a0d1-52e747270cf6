import { createGlobalStyle } from 'styled-components'
import type { Theme } from './theme'

const GlobalStyles = createGlobalStyle<{ theme: Theme }>`
  /* RTL Global Reset */
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    direction: rtl;
    text-align: right;
    scroll-behavior: smooth;
    font-size: 16px;
  }

  body {
    font-family: ${({ theme }) => theme.fonts.primary};
    font-weight: ${({ theme }) => theme.fonts.weights.regular};
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.text.primary};
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
  }

  /* RTL-specific form elements */
  input, textarea, select {
    direction: rtl;
    text-align: right;
    font-family: ${({ theme }) => theme.fonts.primary};
  }

  /* Arabic number formatting */
  .arabic-numbers {
    font-feature-settings: "lnum" 1;
  }

  /* Utility classes for RTL */
  .text-left { text-align: left; }
  .text-right { text-align: right; }
  .text-center { text-align: center; }

  .float-left { float: left; }
  .float-right { float: right; }

  .mr-auto { margin-right: auto; }
  .ml-auto { margin-left: auto; }

  /* Loading animation */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
  }

  @keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
  }

  .loading {
    animation: spin 1s linear infinite;
  }

  .fade-in {
    animation: fadeIn ${({ theme }) => theme.transitions.normal};
  }

  /* Accessibility styles */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: ${({ theme }) => theme.colors.primary};
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;

    &:focus {
      top: 6px;
    }
  }

  /* Focus styles for keyboard navigation */
  .keyboard-navigation *:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }

  /* Default focus styles */
  *:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    * {
      border-color: ButtonText !important;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Button reset */
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }

  /* Link reset */
  a {
    text-decoration: none;
    color: inherit;
  }

  /* List reset */
  ul, ol {
    list-style: none;
  }

  /* Image responsive */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Scrollbar styling for RTL */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.colors.surface};
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.colors.border.medium};
    border-radius: ${({ theme }) => theme.borderRadius.sm};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${({ theme }) => theme.colors.border.dark};
  }

  /* Print styles */
  @media print {
    * {
      -webkit-print-color-adjust: exact !important;
      color-adjust: exact !important;
    }
    
    body {
      font-size: 12pt;
      line-height: 1.4;
    }
    
    .no-print {
      display: none !important;
    }
  }

  /* Responsive breakpoints */
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    html {
      font-size: 14px;
    }
  }

  @media (min-width: ${({ theme }) => theme.breakpoints.wide}) {
    html {
      font-size: 18px;
    }
  }
`

export default GlobalStyles
