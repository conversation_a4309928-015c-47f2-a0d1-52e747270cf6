import React from 'react'
import styled from 'styled-components'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.primary};
`

const PlaceholderText = styled.p`
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1.125rem;
  line-height: 1.6;
`

const ContactPage: React.FC = () => {
  return (
    <Container>
      <Title>اتصل بنا</Title>
      <PlaceholderText>
        صفحة الاتصال قيد التطوير. سيتم إضافة نموذج الاتصال ومعلومات التواصل هنا.
      </PlaceholderText>
    </Container>
  )
}

export default ContactPage
