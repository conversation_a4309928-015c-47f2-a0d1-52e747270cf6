import React, { Suspense, lazy } from 'react'
import { Routes, Route } from 'react-router-dom'
import Layout from '../components/Layout/Layout'
import ProtectedRoute from '../components/Auth/ProtectedRoute'
import LoadingSpinner from '../components/UI/LoadingSpinner'

// Lazy load pages for better performance
// Public Pages
const HomePage = lazy(() => import('../pages/HomePage'))
const ProductsPage = lazy(() => import('../pages/ProductsPage'))
const ProductDetailPage = lazy(() => import('../pages/ProductDetailPage'))
const CartPage = lazy(() => import('../pages/CartPage'))
const CheckoutPage = lazy(() => import('../pages/CheckoutPage'))
const LoginPage = lazy(() => import('../pages/LoginPage'))
const RegisterPage = lazy(() => import('../pages/RegisterPage'))
const ContactPage = lazy(() => import('../pages/ContactPage'))
const AboutPage = lazy(() => import('../pages/AboutPage'))

// Customer Pages
const ProfilePage = lazy(() => import('../pages/Customer/ProfilePage'))
const OrderHistoryPage = lazy(() => import('../pages/Customer/OrderHistoryPage'))
const WishlistPage = lazy(() => import('../pages/Customer/WishlistPage'))

// Order Pages
const OrderConfirmationPage = lazy(() => import('../pages/OrderConfirmationPage'))
const OrderTrackingPage = lazy(() => import('../pages/OrderTrackingPage'))

// Admin Pages
const AdminLayout = lazy(() => import('../components/Admin/AdminLayout'))
const AdminDashboard = lazy(() => import('../pages/Admin/AdminDashboard'))
const AdminProducts = lazy(() => import('../pages/Admin/AdminProducts'))
const AdminOrders = lazy(() => import('../pages/Admin/AdminOrders'))
const AdminCustomers = lazy(() => import('../pages/Admin/AdminCustomers'))
const AdminAnalytics = lazy(() => import('../pages/Admin/AdminAnalytics'))
const AdminCategories = lazy(() => import('../pages/Admin/AdminCategories'))
const AdminDiscounts = lazy(() => import('../pages/Admin/AdminDiscounts'))

// Error Pages
const NotFoundPage = lazy(() => import('../pages/NotFoundPage'))

const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
      {/* Public Routes with Layout */}
      <Route path="/" element={<Layout />}>
        {/* Home */}
        <Route index element={<HomePage />} />
        
        {/* Products */}
        <Route path="products" element={<ProductsPage />} />
        <Route path="products/:id" element={<ProductDetailPage />} />
        <Route path="category/:category" element={<ProductsPage />} />
        
        {/* Shopping */}
        <Route path="cart" element={<CartPage />} />
        <Route path="checkout" element={<CheckoutPage />} />
        <Route path="wishlist" element={<WishlistPage />} />
        
        {/* Auth */}
        <Route path="login" element={<LoginPage />} />
        <Route path="register" element={<RegisterPage />} />
        
        {/* Info Pages */}
        <Route path="contact" element={<ContactPage />} />
        <Route path="about" element={<AboutPage />} />
        
        {/* Order Routes */}
        <Route path="order-confirmation/:orderId" element={<OrderConfirmationPage />} />
        <Route path="orders/:orderId/track" element={<OrderTrackingPage />} />

        {/* Customer Protected Routes */}
        <Route path="profile" element={
          <ProtectedRoute requiredRole="customer">
            <ProfilePage />
          </ProtectedRoute>
        } />
        <Route path="orders" element={
          <ProtectedRoute requiredRole="customer">
            <OrderHistoryPage />
          </ProtectedRoute>
        } />
      </Route>

      {/* Admin Routes */}
      <Route path="/admin" element={
        <ProtectedRoute requiredRole="store_owner">
          <AdminLayout />
        </ProtectedRoute>
      }>
        <Route index element={<AdminDashboard />} />
        <Route path="products" element={<AdminProducts />} />
        <Route path="orders" element={<AdminOrders />} />
        <Route path="customers" element={<AdminCustomers />} />
        <Route path="analytics" element={<AdminAnalytics />} />
        <Route path="categories" element={<AdminCategories />} />
        <Route path="discounts" element={<AdminDiscounts />} />
      </Route>

      {/* 404 Page */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
    </Suspense>
  )
}

export default AppRoutes
