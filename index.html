<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="متجر الملابس العربي - أفضل الأزياء والملابس العصرية" />
    <meta name="keywords" content="ملابس، أزياء، متجر، عربي، قمصان، بناطيل، فساتين" />
    <meta name="author" content="متجر الملابس العربي" />

    <!-- Cairo Font from Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- RTL CSS Reset -->
    <style>
      * {
        box-sizing: border-box;
      }

      html {
        direction: rtl;
        text-align: right;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #ffffff;
        color: #000000;
      }
    </style>

    <title>متجر الملابس العربي</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
