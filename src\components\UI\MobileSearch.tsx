import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { Search, X, Filter } from 'lucide-react'
import Input from './Input'
import Button from './Button'
import Modal from './Modal'
import Select from './Select'

interface MobileSearchProps {
  isOpen: boolean
  onClose: () => void
  onSearch: (query: string, filters: SearchFilters) => void
  placeholder?: string
  initialQuery?: string
  initialFilters?: SearchFilters
}

interface SearchFilters {
  category?: string
  priceRange?: string
  sortBy?: string
}

const SearchContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
`

const SearchHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`

const SearchInputContainer = styled.div`
  flex: 1;
  position: relative;
`

const SearchInput = styled(Input)`
  padding-left: 40px;
  font-size: 1rem;
`

const SearchIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.secondary};
  pointer-events: none;
`

const FiltersSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const FilterLabel = styled.label`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.lg};
`

const QuickFilters = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-top: ${({ theme }) => theme.spacing.md};
`

const QuickFilterChip = styled.button<{ $active: boolean }>`
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background-color: ${({ $active, theme }) => 
    $active ? theme.colors.primary : theme.colors.background};
  color: ${({ $active, theme }) => 
    $active ? 'white' : theme.colors.text.primary};
  font-size: 0.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ $active, theme }) => 
      $active ? theme.colors.primary : theme.colors.surface};
  }
`

const RecentSearches = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
`

const RecentSearchItem = styled.button`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  width: 100%;
  padding: ${({ theme }) => theme.spacing.sm};
  border: none;
  background: none;
  text-align: right;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  cursor: pointer;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`

const MobileSearch: React.FC<MobileSearchProps> = ({
  isOpen,
  onClose,
  onSearch,
  placeholder = 'البحث عن المنتجات...',
  initialQuery = '',
  initialFilters = {}
}) => {
  const [query, setQuery] = useState(initialQuery)
  const [filters, setFilters] = useState<SearchFilters>(initialFilters)
  const [showFilters, setShowFilters] = useState(false)
  
  // Mock recent searches - in real app, this would come from localStorage or API
  const recentSearches = ['قمصان', 'فساتين صيفية', 'جاكيتات شتوية', 'أحذية رياضية']
  
  const quickFilters = [
    { key: 'new', label: 'جديد', category: 'new' },
    { key: 'sale', label: 'تخفيضات', category: 'sale' },
    { key: 'shirts', label: 'قمصان', category: 'shirts' },
    { key: 'dresses', label: 'فساتين', category: 'dresses' },
    { key: 'pants', label: 'بناطيل', category: 'pants' }
  ]
  
  useEffect(() => {
    if (isOpen) {
      setQuery(initialQuery)
      setFilters(initialFilters)
    }
  }, [isOpen, initialQuery, initialFilters])
  
  const handleSearch = () => {
    onSearch(query, filters)
    onClose()
  }
  
  const handleQuickFilter = (category: string) => {
    const newFilters = { ...filters, category }
    setFilters(newFilters)
    onSearch(query, newFilters)
    onClose()
  }
  
  const handleRecentSearch = (searchQuery: string) => {
    setQuery(searchQuery)
    onSearch(searchQuery, filters)
    onClose()
  }
  
  const clearFilters = () => {
    setFilters({})
  }
  
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="البحث والتصفية"
      size="lg"
    >
      <SearchContainer>
        <SearchHeader>
          <SearchInputContainer>
            <SearchInput
              type="text"
              placeholder={placeholder}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              autoFocus
            />
            <SearchIcon>
              <Search size={16} />
            </SearchIcon>
          </SearchInputContainer>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={16} />
          </Button>
        </SearchHeader>
        
        <QuickFilters>
          {quickFilters.map((filter) => (
            <QuickFilterChip
              key={filter.key}
              $active={filters.category === filter.category}
              onClick={() => handleQuickFilter(filter.category)}
            >
              {filter.label}
            </QuickFilterChip>
          ))}
        </QuickFilters>
        
        {showFilters && (
          <FiltersSection>
            <FilterGroup>
              <FilterLabel>الفئة</FilterLabel>
              <Select
                value={filters.category || ''}
                onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                options={[
                  { value: '', label: 'جميع الفئات' },
                  { value: 'shirts', label: 'قمصان' },
                  { value: 'dresses', label: 'فساتين' },
                  { value: 'pants', label: 'بناطيل' },
                  { value: 'jackets', label: 'جاكيتات' }
                ]}
              />
            </FilterGroup>
            
            <FilterGroup>
              <FilterLabel>نطاق السعر</FilterLabel>
              <Select
                value={filters.priceRange || ''}
                onChange={(e) => setFilters({ ...filters, priceRange: e.target.value })}
                options={[
                  { value: '', label: 'جميع الأسعار' },
                  { value: '0-100', label: 'أقل من 100 ر.س' },
                  { value: '100-300', label: '100 - 300 ر.س' },
                  { value: '300-500', label: '300 - 500 ر.س' },
                  { value: '500+', label: 'أكثر من 500 ر.س' }
                ]}
              />
            </FilterGroup>
            
            <FilterGroup>
              <FilterLabel>ترتيب حسب</FilterLabel>
              <Select
                value={filters.sortBy || ''}
                onChange={(e) => setFilters({ ...filters, sortBy: e.target.value })}
                options={[
                  { value: '', label: 'الافتراضي' },
                  { value: 'price-asc', label: 'السعر: من الأقل للأعلى' },
                  { value: 'price-desc', label: 'السعر: من الأعلى للأقل' },
                  { value: 'name', label: 'الاسم' },
                  { value: 'newest', label: 'الأحدث' }
                ]}
              />
            </FilterGroup>
          </FiltersSection>
        )}
        
        {recentSearches.length > 0 && (
          <RecentSearches>
            <FilterLabel>عمليات البحث الأخيرة</FilterLabel>
            {recentSearches.map((search, index) => (
              <RecentSearchItem
                key={index}
                onClick={() => handleRecentSearch(search)}
              >
                <Search size={14} />
                {search}
              </RecentSearchItem>
            ))}
          </RecentSearches>
        )}
        
        <ActionButtons>
          <Button variant="outline" onClick={clearFilters} fullWidth>
            مسح التصفية
          </Button>
          <Button variant="primary" onClick={handleSearch} fullWidth>
            بحث
          </Button>
        </ActionButtons>
      </SearchContainer>
    </Modal>
  )
}

export default MobileSearch
