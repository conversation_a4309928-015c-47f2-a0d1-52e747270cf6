import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

// Arabic number formatting
export const formatArabicNumber = (number: number): string => {
  return new Intl.NumberFormat('ar-SA').format(number)
}

// Currency formatting in Arabic (Saudi Riyal)
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Format currency without symbol
export const formatPrice = (amount: number): string => {
  return new Intl.NumberFormat('ar-SA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Date formatting in Arabic
export const formatDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, 'dd MMMM yyyy', { locale: ar })
}

// Time formatting in Arabic
export const formatTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, 'HH:mm', { locale: ar })
}

// Date and time formatting in Arabic
export const formatDateTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return format(dateObj, 'dd MMMM yyyy - HH:mm', { locale: ar })
}

// Relative time formatting in Arabic
export const formatRelativeTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return 'الآن'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `منذ ${formatArabicNumber(minutes)} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `منذ ${formatArabicNumber(hours)} ${hours === 1 ? 'ساعة' : 'ساعات'}`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `منذ ${formatArabicNumber(days)} ${days === 1 ? 'يوم' : 'أيام'}`
  } else {
    return formatDate(dateObj)
  }
}

// Phone number formatting for Saudi Arabia
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Check if it's a Saudi number
  if (cleaned.startsWith('966')) {
    // International format: +966 XX XXX XXXX
    const match = cleaned.match(/^966(\d{2})(\d{3})(\d{4})$/)
    if (match) {
      return `+966 ${match[1]} ${match[2]} ${match[3]}`
    }
  } else if (cleaned.startsWith('05')) {
    // Local format: 05X XXX XXXX
    const match = cleaned.match(/^(05\d)(\d{3})(\d{4})$/)
    if (match) {
      return `${match[1]} ${match[2]} ${match[3]}`
    }
  }
  
  return phone // Return original if no pattern matches
}

// Percentage formatting
export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  }).format(value / 100)
}

// File size formatting
export const formatFileSize = (bytes: number): string => {
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
  if (bytes === 0) return '0 بايت'
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  const size = bytes / Math.pow(1024, i)
  
  return `${formatArabicNumber(Math.round(size * 100) / 100)} ${sizes[i]}`
}

// Order status formatting
export const formatOrderStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'في الانتظار',
    confirmed: 'مؤكد',
    processing: 'قيد التجهيز',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'ملغي'
  }
  
  return statusMap[status] || status
}

// Payment method formatting
export const formatPaymentMethod = (method: string): string => {
  const methodMap: Record<string, string> = {
    cash_on_delivery: 'الدفع عند الاستلام',
    bank_transfer: 'تحويل بنكي',
    credit_card: 'بطاقة ائتمان'
  }
  
  return methodMap[method] || method
}

// Truncate text with Arabic support
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// Generate order number
export const generateOrderNumber = (): string => {
  const timestamp = Date.now().toString().slice(-8)
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `ORD-${timestamp}-${random}`
}

// Generate product SKU
export const generateSKU = (productName: string, variant?: string): string => {
  const cleanName = productName.replace(/[^a-zA-Z0-9]/g, '').toUpperCase().substring(0, 6)
  const variantCode = variant ? variant.replace(/[^a-zA-Z0-9]/g, '').toUpperCase().substring(0, 3) : ''
  const timestamp = Date.now().toString().slice(-4)
  
  return `${cleanName}${variantCode}${timestamp}`
}
