import React, { useState } from 'react'
import styled from 'styled-components'
import { Plus, Edit, Trash2, Save, X, Folder } from 'lucide-react'
import Button from '../UI/Button'
import Input from '../UI/Input'
import Textarea from '../UI/Textarea'
import Card from '../UI/Card'
import Alert from '../UI/Alert'

interface Category {
  id: string
  name: string
  description: string
  productCount: number
  createdAt: string
}

interface CategoryManagerProps {
  categories: Category[]
  onAddCategory: (category: Omit<Category, 'id' | 'productCount' | 'createdAt'>) => void
  onUpdateCategory: (id: string, category: Partial<Category>) => void
  onDeleteCategory: (id: string) => void
}

const Container = styled(Card)`
  max-width: 800px;
  margin: 0 auto;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const Title = styled.h2`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`

const CategoryForm = styled.div<{ $isVisible: boolean }>`
  display: ${({ $isVisible }) => $isVisible ? 'block' : 'none'};
  padding: ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`

const FormGrid = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const FormActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  justify-content: flex-end;
`

const CategoryList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`

const CategoryItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    border-color: ${({ theme }) => theme.colors.border.medium};
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }
`

const CategoryInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const CategoryName = styled.h3`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const CategoryDescription = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0;
  line-height: 1.4;
`

const CategoryMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  color: ${({ theme }) => theme.colors.text.light};
  font-size: 0.75rem;
`

const ProductCount = styled.span`
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  padding: 2px 8px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: 0.75rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
`

const CategoryActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-right: ${({ theme }) => theme.spacing.md};
`

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  &.edit:hover {
    border-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.primary};
  }
  
  &.delete:hover {
    border-color: ${({ theme }) => theme.colors.status.error};
    color: ${({ theme }) => theme.colors.status.error};
  }
`

const EditForm = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing.sm};
  width: 100%;
  margin-right: ${({ theme }) => theme.spacing.md};
`

const EditActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.secondary};
`

const CategoryManager: React.FC<CategoryManagerProps> = ({
  categories,
  onAddCategory,
  onUpdateCategory,
  onDeleteCategory
}) => {
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })
  const [editData, setEditData] = useState({
    name: '',
    description: ''
  })
  const [errors, setErrors] = useState<string[]>([])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleEditInputChange = (field: string, value: string) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = (data: { name: string; description: string }) => {
    const newErrors: string[] = []
    
    if (!data.name.trim()) {
      newErrors.push('اسم الفئة مطلوب')
    }
    
    if (categories.some(cat => cat.name.toLowerCase() === data.name.toLowerCase() && cat.id !== editingId)) {
      newErrors.push('اسم الفئة موجود بالفعل')
    }
    
    setErrors(newErrors)
    return newErrors.length === 0
  }

  const handleAddCategory = () => {
    if (validateForm(formData)) {
      onAddCategory(formData)
      setFormData({ name: '', description: '' })
      setShowAddForm(false)
      setErrors([])
    }
  }

  const handleStartEdit = (category: Category) => {
    setEditingId(category.id)
    setEditData({
      name: category.name,
      description: category.description
    })
  }

  const handleSaveEdit = () => {
    if (validateForm(editData) && editingId) {
      onUpdateCategory(editingId, editData)
      setEditingId(null)
      setEditData({ name: '', description: '' })
      setErrors([])
    }
  }

  const handleCancelEdit = () => {
    setEditingId(null)
    setEditData({ name: '', description: '' })
    setErrors([])
  }

  const handleDeleteCategory = (id: string) => {
    const category = categories.find(cat => cat.id === id)
    if (category && category.productCount > 0) {
      if (!window.confirm(`هذه الفئة تحتوي على ${category.productCount} منتج. هل أنت متأكد من الحذف؟`)) {
        return
      }
    } else if (!window.confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
      return
    }
    
    onDeleteCategory(id)
  }

  return (
    <Container padding="lg">
      <Header>
        <Title>
          <Folder size={24} />
          إدارة الفئات
        </Title>
        <Button
          variant="primary"
          size="sm"
          onClick={() => setShowAddForm(!showAddForm)}
        >
          <Plus size={16} />
          إضافة فئة جديدة
        </Button>
      </Header>

      {errors.length > 0 && (
        <Alert variant="error" style={{ marginBottom: '1rem' }}>
          <ul style={{ margin: 0, paddingRight: '1rem' }}>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </Alert>
      )}

      <CategoryForm $isVisible={showAddForm}>
        <FormGrid>
          <FormField>
            <Label>اسم الفئة *</Label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="أدخل اسم الفئة"
            />
          </FormField>

          <FormField>
            <Label>وصف الفئة</Label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="أدخل وصف الفئة (اختياري)"
              rows={3}
            />
          </FormField>
        </FormGrid>

        <FormActions>
          <Button variant="primary" onClick={handleAddCategory}>
            <Save size={16} />
            إضافة الفئة
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setShowAddForm(false)
              setFormData({ name: '', description: '' })
              setErrors([])
            }}
          >
            <X size={16} />
            إلغاء
          </Button>
        </FormActions>
      </CategoryForm>

      <CategoryList>
        {categories.length === 0 ? (
          <EmptyState>
            <p>لا توجد فئات حتى الآن. أضف فئة جديدة للبدء.</p>
          </EmptyState>
        ) : (
          categories.map((category) => (
            <CategoryItem key={category.id}>
              {editingId === category.id ? (
                <EditForm>
                  <Input
                    type="text"
                    value={editData.name}
                    onChange={(e) => handleEditInputChange('name', e.target.value)}
                    placeholder="اسم الفئة"
                  />
                  <Textarea
                    value={editData.description}
                    onChange={(e) => handleEditInputChange('description', e.target.value)}
                    placeholder="وصف الفئة"
                    rows={2}
                  />
                  <EditActions>
                    <Button variant="primary" size="sm" onClick={handleSaveEdit}>
                      <Save size={14} />
                      حفظ
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                      <X size={14} />
                      إلغاء
                    </Button>
                  </EditActions>
                </EditForm>
              ) : (
                <CategoryInfo>
                  <CategoryName>{category.name}</CategoryName>
                  {category.description && (
                    <CategoryDescription>{category.description}</CategoryDescription>
                  )}
                  <CategoryMeta>
                    <ProductCount>{category.productCount} منتج</ProductCount>
                    <span>تم الإنشاء: {new Date(category.createdAt).toLocaleDateString('ar-SA')}</span>
                  </CategoryMeta>
                </CategoryInfo>
              )}

              {editingId !== category.id && (
                <CategoryActions>
                  <ActionButton
                    className="edit"
                    onClick={() => handleStartEdit(category)}
                    title="تعديل"
                  >
                    <Edit size={16} />
                  </ActionButton>
                  <ActionButton
                    className="delete"
                    onClick={() => handleDeleteCategory(category.id)}
                    title="حذف"
                  >
                    <Trash2 size={16} />
                  </ActionButton>
                </CategoryActions>
              )}
            </CategoryItem>
          ))
        )}
      </CategoryList>
    </Container>
  )
}

export default CategoryManager
