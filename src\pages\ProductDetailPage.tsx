import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { Heart, ShoppingCart, Share2, Star, Plus, Minus } from 'lucide-react'
import { useProducts } from '../context/ProductContext'
import { useCart } from '../context/CartContext'
import { useWishlist } from '../context/WishlistContext'
import { formatPrice } from '../utils/formatters'
import Breadcrumb from '../components/UI/Breadcrumb'
import Button from '../components/UI/Button'
import Badge from '../components/UI/Badge'
import Select from '../components/UI/Select'
import Alert from '../components/UI/Alert'
import ProductCard from '../components/Product/ProductCard'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const ProductSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.xl};
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing.lg};
  }
`

const ImageGallery = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`

const MainImage = styled.img`
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`

const ThumbnailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: ${({ theme }) => theme.spacing.sm};
`

const Thumbnail = styled.img<{ $active: boolean }>`
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  border: 2px solid ${({ $active, theme }) =>
    $active ? theme.colors.primary : theme.colors.border.light};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary};
  }
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`

const ProductHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: ${({ theme }) => theme.spacing.md};
`

const ProductTitle = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: 1.3;
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`

const IconButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ $active, theme }) =>
    $active ? theme.colors.primary : theme.colors.background};
  color: ${({ $active, theme }) =>
    $active ? theme.colors.text.white : theme.colors.text.primary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ $active, theme }) =>
      $active ? theme.colors.accent : theme.colors.surface};
  }
`

const ProductMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`

const Category = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`

const PriceSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  margin: ${({ theme }) => theme.spacing.md} 0;
`

const CurrentPrice = styled.span`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.primary};
`

const OriginalPrice = styled.span`
  font-size: 1.25rem;
  color: ${({ theme }) => theme.colors.text.light};
  text-decoration: line-through;
`

const Description = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.6;
  margin: ${({ theme }) => theme.spacing.md} 0;
`

const VariantSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
`

const VariantRow = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    align-items: stretch;
  }
`

const QuantitySelector = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.xs};
`

const QuantityButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  border-radius: ${({ theme }) => theme.borderRadius.sm};

  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.colors.surface};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const QuantityDisplay = styled.span`
  min-width: 40px;
  text-align: center;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
`

const StockInfo = styled.div<{ $inStock: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  color: ${({ $inStock, theme }) =>
    $inStock ? theme.colors.status.success : theme.colors.status.error};
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  font-size: 0.875rem;
`

const AddToCartSection = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`

const RelatedProducts = styled.section`
  margin-top: ${({ theme }) => theme.spacing.xl};
`

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  text-align: center;
`

const ProductGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
`

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { products } = useProducts()
  const { addToCart } = useCart()
  const { items: wishlistItems, addToWishlist, removeFromWishlist } = useWishlist()

  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedColor, setSelectedColor] = useState('')
  const [quantity, setQuantity] = useState(1)
  const [showAlert, setShowAlert] = useState(false)

  const product = products.find(p => p.id === id)
  const isInWishlist = product ? wishlistItems.includes(product.id) : false

  useEffect(() => {
    if (!product) {
      navigate('/products')
    }
  }, [product, navigate])

  if (!product) {
    return null
  }

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'المنتجات', href: '/products' },
    { label: product.name, current: true }
  ]

  const isOnSale = product.salePrice && product.salePrice < product.price
  const discountPercentage = isOnSale
    ? Math.round(((product.price - product.salePrice!) / product.price) * 100)
    : 0

  const isInStock = product.stock && product.stock > 0
  const maxQuantity = Math.min(product.stock || 0, 10)

  const sizeOptions = product.sizes?.map(size => ({ value: size, label: size })) || []
  const colorOptions = product.colors?.map(color => ({ value: color, label: color })) || []

  const relatedProducts = products
    .filter(p => p.id !== product.id && p.category === product.category)
    .slice(0, 4)

  const handleWishlistToggle = () => {
    if (isInWishlist) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product.id)
    }
  }

  const handleAddToCart = () => {
    if (!isInStock) return

    addToCart({
      id: product.id,
      name: product.name,
      price: product.salePrice || product.price,
      image: product.images[selectedImageIndex],
      quantity,
      stock: product.stock,
      size: selectedSize || undefined,
      color: selectedColor || undefined
    })

    setShowAlert(true)
    setTimeout(() => setShowAlert(false), 3000)
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: product.description,
          url: window.location.href
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      {showAlert && (
        <Alert variant="success" dismissible onDismiss={() => setShowAlert(false)}>
          تم إضافة المنتج إلى سلة التسوق بنجاح!
        </Alert>
      )}

      <ProductSection>
        <ImageGallery>
          <MainImage
            src={product.images[selectedImageIndex]}
            alt={product.name}
          />
          <ThumbnailGrid>
            {product.images.map((image, index) => (
              <Thumbnail
                key={index}
                src={image}
                alt={`${product.name} ${index + 1}`}
                $active={index === selectedImageIndex}
                onClick={() => setSelectedImageIndex(index)}
              />
            ))}
          </ThumbnailGrid>
        </ImageGallery>

        <ProductInfo>
          <ProductHeader>
            <div>
              <Category>{product.category}</Category>
              <ProductTitle>{product.name}</ProductTitle>
            </div>
            <ActionButtons>
              <IconButton
                $active={isInWishlist}
                onClick={handleWishlistToggle}
                aria-label={isInWishlist ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
              >
                <Heart size={20} fill={isInWishlist ? 'currentColor' : 'none'} />
              </IconButton>
              <IconButton onClick={handleShare} aria-label="مشاركة">
                <Share2 size={20} />
              </IconButton>
            </ActionButtons>
          </ProductHeader>

          <PriceSection>
            <CurrentPrice>
              {formatPrice(product.salePrice || product.price)}
            </CurrentPrice>
            {isOnSale && (
              <>
                <OriginalPrice>
                  {formatPrice(product.price)}
                </OriginalPrice>
                <Badge variant="error" size="sm">
                  -{discountPercentage}%
                </Badge>
              </>
            )}
          </PriceSection>

          <Description>{product.description}</Description>

          <StockInfo $inStock={!!isInStock}>
            {isInStock ? `متوفر (${product.stock} قطعة)` : 'غير متوفر'}
          </StockInfo>

          {isInStock && (
            <VariantSection>
              {sizeOptions.length > 0 && (
                <VariantRow>
                  <label>المقاس:</label>
                  <Select
                    placeholder="اختر المقاس"
                    value={selectedSize}
                    onChange={(e) => setSelectedSize(e.target.value)}
                    options={sizeOptions}
                  />
                </VariantRow>
              )}

              {colorOptions.length > 0 && (
                <VariantRow>
                  <label>اللون:</label>
                  <Select
                    placeholder="اختر اللون"
                    value={selectedColor}
                    onChange={(e) => setSelectedColor(e.target.value)}
                    options={colorOptions}
                  />
                </VariantRow>
              )}

              <VariantRow>
                <label>الكمية:</label>
                <QuantitySelector>
                  <QuantityButton
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                  >
                    <Minus size={16} />
                  </QuantityButton>
                  <QuantityDisplay>{quantity}</QuantityDisplay>
                  <QuantityButton
                    onClick={() => setQuantity(Math.min(maxQuantity, quantity + 1))}
                    disabled={quantity >= maxQuantity}
                  >
                    <Plus size={16} />
                  </QuantityButton>
                </QuantitySelector>
              </VariantRow>
            </VariantSection>
          )}

          <AddToCartSection>
            <Button
              variant="primary"
              size="lg"
              onClick={handleAddToCart}
              disabled={!isInStock}
              fullWidth
            >
              <ShoppingCart size={20} />
              {isInStock ? 'أضف إلى السلة' : 'غير متوفر'}
            </Button>
          </AddToCartSection>
        </ProductInfo>
      </ProductSection>

      {relatedProducts.length > 0 && (
        <RelatedProducts>
          <SectionTitle>منتجات ذات صلة</SectionTitle>
          <ProductGrid>
            {relatedProducts.map((relatedProduct) => (
              <ProductCard key={relatedProduct.id} product={relatedProduct} />
            ))}
          </ProductGrid>
        </RelatedProducts>
      )}
    </Container>
  )
}

export default ProductDetailPage
