import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { ShoppingCart, Heart, User, Menu, X, Search } from 'lucide-react'
import { useAuth } from '../../context/AuthContext'
import { useCart } from '../../context/CartContext'
import { useWishlist } from '../../context/WishlistContext'
import Button from '../UI/Button'
import MobileSearch from '../UI/MobileSearch'

interface HeaderProps {
  isAdmin?: boolean
}

const HeaderContainer = styled.header`
  background-color: ${({ theme }) => theme.colors.background};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  position: sticky;
  top: 0;
  z-index: ${({ theme }) => theme.zIndex.sticky};
`

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: 0 ${({ theme }) => theme.spacing.md};
  }
`

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
  gap: ${({ theme }) => theme.spacing.lg};
`

const Logo = styled(Link)`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.primary};
  text-decoration: none;
  
  &:hover {
    color: ${({ theme }) => theme.colors.accent};
  }
`

const Nav = styled.nav<{ $isOpen: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    position: fixed;
    top: 70px;
    right: 0;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: ${({ theme }) => theme.colors.background};
    flex-direction: column;
    justify-content: flex-start;
    padding: ${({ theme }) => theme.spacing.xl};
    transform: translateX(${({ $isOpen }) => ($isOpen ? '0' : '100%')});
    transition: transform ${({ theme }) => theme.transitions.normal};
    border-left: 1px solid ${({ theme }) => theme.colors.border.light};
  }
`

const NavLink = styled(Link)`
  color: ${({ theme }) => theme.colors.text.primary};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  padding: ${({ theme }) => theme.spacing.sm} 0;
  transition: color ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`

const Actions = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`

const IconButton = styled.button`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }
`

const Badge = styled.span`
  position: absolute;
  top: -2px;
  left: -2px;
  background-color: ${({ theme }) => theme.colors.status.error};
  color: white;
  font-size: 0.75rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  padding: 2px 6px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
`

const MobileMenuButton = styled(IconButton)`
  display: none;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: flex;
  }
`

const SearchContainer = styled.div`
  flex: 1;
  max-width: 400px;
  position: relative;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
`

const SearchInput = styled.input`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  padding-left: 40px;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-family: ${({ theme }) => theme.fonts.primary};
  direction: rtl;
  text-align: right;
  
  &::placeholder {
    color: ${({ theme }) => theme.colors.text.light};
  }
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.light};
  width: 18px;
  height: 18px;
`

const MobileSearchButton = styled(MobileMenuButton)`
  display: none;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: flex;
  }
`

const Header: React.FC<HeaderProps> = ({ isAdmin = false }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false)

  const { user, isAuthenticated, logout } = useAuth()
  const { itemCount } = useCart()
  const { itemCount: wishlistCount } = useWishlist()
  const navigate = useNavigate()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchQuery.trim())}`)
      setSearchQuery('')
    }
  }

  const handleLogout = () => {
    logout()
    navigate('/')
    setIsMenuOpen(false)
  }

  const handleMobileSearch = (query: string, filters: any) => {
    const searchParams = new URLSearchParams()
    if (query.trim()) {
      searchParams.set('search', query.trim())
    }
    if (filters.category) {
      searchParams.set('category', filters.category)
    }
    if (filters.priceRange) {
      searchParams.set('priceRange', filters.priceRange)
    }
    if (filters.sortBy) {
      searchParams.set('sortBy', filters.sortBy)
    }

    const queryString = searchParams.toString()
    navigate(`/products${queryString ? `?${queryString}` : ''}`)
  }

  return (
    <HeaderContainer>
      <Container>
        <HeaderContent>
          <Logo to={isAdmin ? '/admin' : '/'}>
            {isAdmin ? 'لوحة التحكم' : 'متجر الملابس'}
          </Logo>

          {!isAdmin && (
            <SearchContainer>
              <form onSubmit={handleSearch}>
                <SearchInput
                  type="text"
                  placeholder="البحث عن المنتجات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <SearchIcon />
              </form>
            </SearchContainer>
          )}

          <Nav $isOpen={isMenuOpen}>
            {isAdmin ? (
              <>
                <NavLink to="/admin" onClick={() => setIsMenuOpen(false)}>الرئيسية</NavLink>
                <NavLink to="/admin/products" onClick={() => setIsMenuOpen(false)}>المنتجات</NavLink>
                <NavLink to="/admin/orders" onClick={() => setIsMenuOpen(false)}>الطلبات</NavLink>
                <NavLink to="/admin/customers" onClick={() => setIsMenuOpen(false)}>العملاء</NavLink>
                <NavLink to="/admin/analytics" onClick={() => setIsMenuOpen(false)}>التقارير</NavLink>
              </>
            ) : (
              <>
                <NavLink to="/" onClick={() => setIsMenuOpen(false)}>الرئيسية</NavLink>
                <NavLink to="/products" onClick={() => setIsMenuOpen(false)}>المنتجات</NavLink>
                <NavLink to="/about" onClick={() => setIsMenuOpen(false)}>من نحن</NavLink>
                <NavLink to="/contact" onClick={() => setIsMenuOpen(false)}>اتصل بنا</NavLink>
              </>
            )}
          </Nav>

          <Actions>
            {!isAdmin && (
              <>
                <MobileSearchButton onClick={() => setIsMobileSearchOpen(true)}>
                  <Search size={20} />
                </MobileSearchButton>

                <IconButton as={Link} to="/wishlist">
                  <Heart size={20} />
                  {wishlistCount > 0 && <Badge>{wishlistCount}</Badge>}
                </IconButton>

                <IconButton as={Link} to="/cart">
                  <ShoppingCart size={20} />
                  {itemCount > 0 && <Badge>{itemCount}</Badge>}
                </IconButton>
              </>
            )}

            {isAuthenticated && user ? (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '0.875rem' }}>{user.name}</span>
                <Button variant="ghost" size="sm" onClick={handleLogout}>
                  تسجيل الخروج
                </Button>
              </div>
            ) : (
              <IconButton as={Link} to="/login">
                <User size={20} />
              </IconButton>
            )}

            <MobileMenuButton onClick={() => setIsMenuOpen(!isMenuOpen)}>
              {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
            </MobileMenuButton>
          </Actions>
        </HeaderContent>
      </Container>

      {!isAdmin && (
        <MobileSearch
          isOpen={isMobileSearchOpen}
          onClose={() => setIsMobileSearchOpen(false)}
          onSearch={handleMobileSearch}
          initialQuery={searchQuery}
        />
      )}
    </HeaderContainer>
  )
}

export default Header
