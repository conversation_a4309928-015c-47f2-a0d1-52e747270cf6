import React from 'react'
import styled from 'styled-components'
import { Heart, ShoppingCart, Eye } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useWishlist } from '../../context/WishlistContext'
import { useCart } from '../../context/CartContext'
import { formatPrice } from '../../utils/formatters'
import Button from '../UI/Button'
import Card from '../UI/Card'
import Badge from '../UI/Badge'
import type { Product } from '../../types'

interface ProductCardProps {
  product: Product
}

const ProductCardContainer = styled(Card)`
  position: relative;
  overflow: hidden;
  transition: all ${({ theme }) => theme.transitions.normal};
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
`

const ImageContainer = styled.div`
  position: relative;
  overflow: hidden;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const ProductImage = styled.img`
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform ${({ theme }) => theme.transitions.normal};
  cursor: pointer;
  
  ${ProductCardContainer}:hover & {
    transform: scale(1.05);
  }
`

const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity ${({ theme }) => theme.transitions.normal};
  
  ${ProductCardContainer}:hover & {
    opacity: 1;
  }
`

const QuickActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background-color: rgba(255, 255, 255, 0.9);
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: white;
    transform: scale(1.1);
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }
`

const WishlistButton = styled(ActionButton)<{ $isInWishlist: boolean }>`
  color: ${({ $isInWishlist, theme }) => 
    $isInWishlist ? theme.colors.status.error : theme.colors.text.primary};
  
  &:hover {
    color: ${({ theme }) => theme.colors.status.error};
  }
`

const BadgeContainer = styled.div`
  position: absolute;
  top: ${({ theme }) => theme.spacing.sm};
  right: ${({ theme }) => theme.spacing.sm};
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  z-index: 1;
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ProductCategory = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.light};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`

const ProductName = styled.h3`
  font-size: 1rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: 1.4;
  cursor: pointer;
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`

const PriceSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  margin: ${({ theme }) => theme.spacing.sm} 0;
`

const CurrentPrice = styled.span`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.primary};
`

const OriginalPrice = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.light};
  text-decoration: line-through;
`

const StockStatus = styled.div<{ $inStock: boolean }>`
  font-size: 0.75rem;
  color: ${({ $inStock, theme }) => 
    $inStock ? theme.colors.status.success : theme.colors.status.error};
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`

const AddToCartButton = styled(Button)`
  width: 100%;
  margin-top: ${({ theme }) => theme.spacing.sm};
`

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const { items: wishlistItems, addToWishlist, removeFromWishlist } = useWishlist()
  const { addToCart } = useCart()
  const navigate = useNavigate()

  const isInWishlist = wishlistItems.includes(product.id)
  const isOnSale = product.salePrice && product.salePrice < product.price
  const discountPercentage = isOnSale 
    ? Math.round(((product.price - product.salePrice!) / product.price) * 100)
    : 0
  const isInStock = product.stock && product.stock > 0
  const isLowStock = product.stock && product.stock <= 5

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isInWishlist) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product.id)
    }
  }

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isInStock) {
      addToCart({
        id: product.id,
        name: product.name,
        price: product.salePrice || product.price,
        image: product.images[0],
        quantity: 1,
        stock: product.stock
      })
    }
  }

  const handleViewProduct = () => {
    navigate(`/products/${product.id}`)
  }

  const handleQuickView = (e: React.MouseEvent) => {
    e.stopPropagation()
    // TODO: Implement quick view modal
    handleViewProduct()
  }

  return (
    <ProductCardContainer padding="md" hoverable clickable onClick={handleViewProduct}>
      <ImageContainer>
        <ProductImage
          src={product.images[0]}
          alt={product.name}
          loading="lazy"
        />
        
        <ImageOverlay>
          <QuickActions>
            <WishlistButton
              $isInWishlist={isInWishlist}
              onClick={handleWishlistToggle}
              aria-label={isInWishlist ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
            >
              <Heart size={18} fill={isInWishlist ? 'currentColor' : 'none'} />
            </WishlistButton>
            
            <ActionButton
              onClick={handleQuickView}
              aria-label="عرض سريع"
            >
              <Eye size={18} />
            </ActionButton>
          </QuickActions>
        </ImageOverlay>
        
        <BadgeContainer>
          {isOnSale && (
            <Badge variant="error" size="sm">
              -{discountPercentage}%
            </Badge>
          )}
          {isLowStock && isInStock && (
            <Badge variant="warning" size="sm">
              كمية محدودة
            </Badge>
          )}
          {!isInStock && (
            <Badge variant="secondary" size="sm">
              غير متوفر
            </Badge>
          )}
        </BadgeContainer>
      </ImageContainer>

      <ProductInfo>
        <ProductCategory>{product.category}</ProductCategory>
        
        <ProductName>{product.name}</ProductName>

        <PriceSection>
          <CurrentPrice>
            {formatPrice(product.salePrice || product.price)}
          </CurrentPrice>
          
          {isOnSale && (
            <OriginalPrice>
              {formatPrice(product.price)}
            </OriginalPrice>
          )}
        </PriceSection>

        <StockStatus $inStock={!!isInStock}>
          {isInStock ? `متوفر (${product.stock})` : 'غير متوفر'}
        </StockStatus>

        <AddToCartButton
          variant="primary"
          size="sm"
          onClick={handleAddToCart}
          disabled={!isInStock}
        >
          <ShoppingCart size={16} />
          {isInStock ? 'أضف للسلة' : 'غير متوفر'}
        </AddToCartButton>
      </ProductInfo>
    </ProductCardContainer>
  )
}

export default ProductCard
