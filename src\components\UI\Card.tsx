import React from 'react'
import styled, { css } from 'styled-components'
import type { ReactNode } from 'react'

interface CardProps {
  children: ReactNode
  variant?: 'default' | 'outlined' | 'elevated'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  hoverable?: boolean
  clickable?: boolean
  onClick?: () => void
  className?: string
}

const getVariantStyles = (variant: string) => {
  switch (variant) {
    case 'outlined':
      return css`
        background: ${({ theme }) => theme.colors.background};
        border: 1px solid ${({ theme }) => theme.colors.border.medium};
        box-shadow: none;
      `
    case 'elevated':
      return css`
        background: ${({ theme }) => theme.colors.background};
        border: none;
        box-shadow: ${({ theme }) => theme.shadows.lg};
      `
    default:
      return css`
        background: ${({ theme }) => theme.colors.background};
        border: 1px solid ${({ theme }) => theme.colors.border.light};
        box-shadow: ${({ theme }) => theme.shadows.sm};
      `
  }
}

const getPaddingStyles = (padding: string) => {
  switch (padding) {
    case 'none':
      return 'padding: 0;'
    case 'sm':
      return css`padding: ${({ theme }) => theme.spacing.sm};`
    case 'lg':
      return css`padding: ${({ theme }) => theme.spacing.lg};`
    default:
      return css`padding: ${({ theme }) => theme.spacing.md};`
  }
}

const StyledCard = styled.div<{
  $variant: string
  $padding: string
  $hoverable: boolean
  $clickable: boolean
}>`
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  transition: all ${({ theme }) => theme.transitions.normal};
  
  ${({ $variant }) => getVariantStyles($variant)}
  ${({ $padding }) => getPaddingStyles($padding)}
  
  ${({ $hoverable, theme }) =>
    $hoverable &&
    css`
      &:hover {
        transform: translateY(-2px);
        box-shadow: ${theme.shadows.lg};
      }
    `}
  
  ${({ $clickable }) =>
    $clickable &&
    css`
      cursor: pointer;
      
      &:active {
        transform: translateY(0);
      }
    `}
`

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  hoverable = false,
  clickable = false,
  onClick,
  className,
  ...props
}) => {
  return (
    <StyledCard
      $variant={variant}
      $padding={padding}
      $hoverable={hoverable}
      $clickable={clickable || !!onClick}
      onClick={onClick}
      className={className}
      {...props}
    >
      {children}
    </StyledCard>
  )
}

export default Card
