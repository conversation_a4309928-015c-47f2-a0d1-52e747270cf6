import React from 'react'
import styled from 'styled-components'
import { useWishlist } from '../../context/WishlistContext'
import { useProducts } from '../../context/ProductContext'
import WishlistItem from '../../components/Wishlist/WishlistItem'
import Breadcrumb from '../../components/UI/Breadcrumb'
import Button from '../../components/UI/Button'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.primary};
`

const WishlistGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-top: ${({ theme }) => theme.spacing.lg};
`

const EmptyWishlist = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.secondary};
`

const EmptyWishlistTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const EmptyWishlistText = styled.p`
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const WishlistActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const ItemCount = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const WishlistPage: React.FC = () => {
  const { items, clearWishlist } = useWishlist()
  const { products } = useProducts()

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'المفضلة', current: true }
  ]

  // Get full product details for wishlist items
  const wishlistProducts = items
    .map(itemId => products.find(product => product.id === itemId))
    .filter(Boolean)

  if (items.length === 0) {
    return (
      <Container>
        <BreadcrumbWrapper>
          <Breadcrumb items={breadcrumbItems} />
        </BreadcrumbWrapper>

        <Title>المفضلة</Title>

        <EmptyWishlist>
          <EmptyWishlistTitle>قائمة المفضلة فارغة</EmptyWishlistTitle>
          <EmptyWishlistText>
            لم تقم بإضافة أي منتجات إلى قائمة المفضلة بعد.
            تصفح مجموعتنا الواسعة من المنتجات وأضف ما يعجبك إلى المفضلة.
          </EmptyWishlistText>
          <Button variant="primary" size="lg" onClick={() => window.location.href = '/products'}>
            تصفح المنتجات
          </Button>
        </EmptyWishlist>
      </Container>
    )
  }

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <Title>المفضلة</Title>

      <WishlistActions>
        <ItemCount>
          {items.length} {items.length === 1 ? 'منتج' : 'منتجات'}
        </ItemCount>
        <Button variant="ghost" size="sm" onClick={clearWishlist}>
          إفراغ المفضلة
        </Button>
      </WishlistActions>

      <WishlistGrid>
        {wishlistProducts.map((product) => (
          product && <WishlistItem key={product.id} product={product} />
        ))}
      </WishlistGrid>
    </Container>
  )
}

export default WishlistPage
