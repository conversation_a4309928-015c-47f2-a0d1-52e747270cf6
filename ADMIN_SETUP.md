# دليل إعداد حساب المدير - Admin Setup Guide

## 🔐 **بيانات تسجيل الدخول للمدير**

### **طريقة الوصول للوحة الإدارة:**

1. **انتقل إلى صفحة تسجيل الدخول:** `http://localhost:3002/login`

2. **استخدم أي بريد إلكتروني يحتوي على كلمة "admin":**
   - `<EMAIL>`
   - `<EMAIL>`
   - `<EMAIL>`
   - `<EMAIL>`

3. **كلمة المرور:** أي كلمة مرور (النظام تجريبي حالياً)

4. **بعد تسجيل الدخول بنجاح، انتقل إلى:** `http://localhost:3002/admin`

---

## 🎛️ **وحة الإدارة الشاملة**

### **الصفحات المتاحة:**

- **`/admin`** - لوحة التحكم الرئيسية مع الإحصائيات
- **`/admin/products`** - إدارة المنتجات
- **`/admin/orders`** - إدارة الطلبات
- **`/admin/customers`** - إدارة العملاء
- **`/admin/categories`** - إدارة الفئات
- **`/admin/discounts`** - إدارة الخصومات والعروض
- **`/admin/analytics`** - التقارير والتحليلات

### **الميزات المتاحة:**

✅ **إدارة المنتجات الكاملة**
- إضافة منتجات جديدة
- تعديل المنتجات الموجودة
- حذف المنتجات
- إدارة المخزون
- إدارة الصور والمتغيرات

✅ **إدارة الطلبات**
- عرض جميع الطلبات
- تحديث حالة الطلبات
- طباعة الفواتير
- تتبع الشحن

✅ **إدارة العملاء**
- عرض قائمة العملاء
- تفاصيل العملاء
- إحصائيات العملاء
- إرسال رسائل للعملاء

✅ **إدارة الفئات**
- إضافة فئات جديدة
- تعديل الفئات
- حذف الفئات
- ترتيب الفئات

✅ **إدارة الخصومات**
- إنشاء كوبونات خصم
- إدارة العروض الترويجية
- تحديد شروط الخصم
- متابعة استخدام الكوبونات

✅ **التقارير والتحليلات**
- إحصائيات المبيعات
- تقارير الإيرادات
- تحليل سلوك العملاء
- مؤشرات الأداء

---

## 🔧 **كيفية عمل النظام:**

1. **التحقق من الصلاحيات:** النظام يتحقق من أن البريد الإلكتروني يحتوي على "admin"
2. **تعيين الدور:** يتم تعيين دور `store_owner` تلقائياً
3. **الحماية:** جميع صفحات الإدارة محمية بـ `ProtectedRoute`
4. **التخزين:** بيانات المدير تُحفظ في `localStorage`

---

## 📝 **ملاحظات مهمة:**

- النظام حالياً في وضع التطوير مع بيانات تجريبية
- لا يتطلب كلمة مرور محددة (أي كلمة مرور تعمل)
- البيانات تُحفظ محلياً في المتصفح
- لتطبيق الإنتاج، يجب ربط النظام بقاعدة بيانات حقيقية

---

## 🚀 **للتطوير المستقبلي:**

1. **ربط قاعدة البيانات:** استبدال البيانات التجريبية بـ API حقيقي
2. **نظام مصادقة متقدم:** JWT tokens وتشفير كلمات المرور
3. **صلاحيات متدرجة:** أدوار مختلفة للمديرين
4. **نسخ احتياطية:** نظام حفظ واستعادة البيانات
