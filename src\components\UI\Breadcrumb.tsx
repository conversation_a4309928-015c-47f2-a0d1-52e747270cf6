import React from 'react'
import { Link } from 'react-router-dom'
import styled from 'styled-components'
import { ChevronLeft } from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  separator?: React.ReactNode
  className?: string
}

const BreadcrumbContainer = styled.nav`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const BreadcrumbList = styled.ol`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  list-style: none;
  margin: 0;
  padding: 0;
`

const BreadcrumbItem = styled.li`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
`

const BreadcrumbLink = styled(Link)`
  color: ${({ theme }) => theme.colors.text.secondary};
  text-decoration: none;
  transition: color ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary};
    text-decoration: underline;
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
    border-radius: ${({ theme }) => theme.borderRadius.sm};
  }
`

const BreadcrumbText = styled.span<{ $current?: boolean }>`
  color: ${({ $current, theme }) => 
    $current ? theme.colors.text.primary : theme.colors.text.secondary};
  font-weight: ${({ $current, theme }) => 
    $current ? theme.fonts.weights.medium : theme.fonts.weights.normal};
`

const Separator = styled.span`
  color: ${({ theme }) => theme.colors.text.light};
  display: flex;
  align-items: center;
`

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items,
  separator = <ChevronLeft size={14} />,
  className
}) => {
  return (
    <BreadcrumbContainer className={className} aria-label="مسار التنقل">
      <BreadcrumbList>
        {items.map((item, index) => (
          <BreadcrumbItem key={index}>
            {index > 0 && <Separator>{separator}</Separator>}
            
            {item.href && !item.current ? (
              <BreadcrumbLink to={item.href}>
                {item.label}
              </BreadcrumbLink>
            ) : (
              <BreadcrumbText 
                $current={item.current}
                aria-current={item.current ? 'page' : undefined}
              >
                {item.label}
              </BreadcrumbText>
            )}
          </BreadcrumbItem>
        ))}
      </BreadcrumbList>
    </BreadcrumbContainer>
  )
}

export default Breadcrumb
