import React from 'react'
import styled, { css } from 'styled-components'
import { AlertCircle, CheckCircle, Info, AlertTriangle, X } from 'lucide-react'
import type { ReactNode } from 'react'

interface AlertProps {
  children: ReactNode
  variant?: 'success' | 'warning' | 'error' | 'info'
  title?: string
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
}

const getVariantStyles = (variant: string) => {
  switch (variant) {
    case 'success':
      return css`
        background-color: ${({ theme }) => theme.colors.status.success}10;
        border-color: ${({ theme }) => theme.colors.status.success}30;
        color: ${({ theme }) => theme.colors.status.success};
      `
    case 'warning':
      return css`
        background-color: ${({ theme }) => theme.colors.status.warning}10;
        border-color: ${({ theme }) => theme.colors.status.warning}30;
        color: ${({ theme }) => theme.colors.status.warning};
      `
    case 'error':
      return css`
        background-color: ${({ theme }) => theme.colors.status.error}10;
        border-color: ${({ theme }) => theme.colors.status.error}30;
        color: ${({ theme }) => theme.colors.status.error};
      `
    case 'info':
      return css`
        background-color: ${({ theme }) => theme.colors.status.info}10;
        border-color: ${({ theme }) => theme.colors.status.info}30;
        color: ${({ theme }) => theme.colors.status.info};
      `
    default:
      return css`
        background-color: ${({ theme }) => theme.colors.status.info}10;
        border-color: ${({ theme }) => theme.colors.status.info}30;
        color: ${({ theme }) => theme.colors.status.info};
      `
  }
}

const getIcon = (variant: string) => {
  switch (variant) {
    case 'success':
      return CheckCircle
    case 'warning':
      return AlertTriangle
    case 'error':
      return AlertCircle
    case 'info':
      return Info
    default:
      return Info
  }
}

const AlertContainer = styled.div<{ $variant: string }>`
  display: flex;
  align-items: flex-start;
  gap: ${({ theme }) => theme.spacing.sm};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-family: ${({ theme }) => theme.fonts.primary};
  
  ${({ $variant }) => getVariantStyles($variant)}
`

const IconWrapper = styled.div`
  flex-shrink: 0;
  margin-top: 2px;
`

const Content = styled.div`
  flex: 1;
  min-width: 0;
`

const Title = styled.div`
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.text.primary};
`

const Message = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.5;
`

const DismissButton = styled.button`
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  color: currentColor;
  cursor: pointer;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  opacity: 0.7;
  transition: opacity ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    opacity: 1;
  }
  
  &:focus {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
`

const Alert: React.FC<AlertProps> = ({
  children,
  variant = 'info',
  title,
  dismissible = false,
  onDismiss,
  className,
  ...props
}) => {
  const IconComponent = getIcon(variant)

  return (
    <AlertContainer $variant={variant} className={className} {...props}>
      <IconWrapper>
        <IconComponent size={20} />
      </IconWrapper>
      
      <Content>
        {title && <Title>{title}</Title>}
        <Message>{children}</Message>
      </Content>
      
      {dismissible && onDismiss && (
        <DismissButton onClick={onDismiss} aria-label="إغلاق التنبيه">
          <X size={16} />
        </DismissButton>
      )}
    </AlertContainer>
  )
}

export default Alert
