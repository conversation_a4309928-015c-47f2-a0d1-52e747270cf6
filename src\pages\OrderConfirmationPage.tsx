import React from 'react'
import styled from 'styled-components'
import { CheckCircle, Package, Download, Eye, ArrowLeft } from 'lucide-react'
import { useParams } from 'react-router-dom'
import { formatPrice, formatDate } from '../utils/formatters'
import Breadcrumb from '../components/UI/Breadcrumb'
import Button from '../components/UI/Button'
import Card from '../components/UI/Card'
import Badge from '../components/UI/Badge'

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const SuccessHeader = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const SuccessIcon = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const SuccessTitle = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.status.success};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`

const SuccessMessage = styled.p`
  font-size: 1.125rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
  line-height: 1.6;
`

const OrderDetails = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const OrderHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
  }
`

const OrderInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const OrderNumber = styled.h2`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`

const OrderMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const OrderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    width: 100%;
    justify-content: stretch;
  }
`

const SectionTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`

const InfoSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`

const InfoLabel = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const InfoValue = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.5;
`

const OrderItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const OrderItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ItemImage = styled.img`
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ItemInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ItemName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ItemDetails = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const ItemPrice = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ItemTotal = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const OrderSummary = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  padding: ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  
  &:last-child {
    margin-bottom: 0;
    padding-top: ${({ theme }) => theme.spacing.md};
    border-top: 1px solid ${({ theme }) => theme.colors.border.light};
    font-weight: ${({ theme }) => theme.fonts.weights.bold};
    font-size: 1.125rem;
  }
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  justify-content: center;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`

// Mock order data
const mockOrder = {
  id: 'ORD-2024-001',
  date: '2024-01-15T10:30:00Z',
  status: 'confirmed',
  estimatedDelivery: '2024-01-20',
  total: 324.99,
  subtotal: 299.99,
  shipping: 25.00,
  tax: 44.99,
  shippingAddress: {
    name: 'أحمد محمد',
    address: 'شارع الملك فهد، حي النخيل',
    city: 'الرياض',
    postalCode: '12345',
    phone: '+966501234567'
  },
  paymentMethod: 'بطاقة ائتمان',
  items: [
    {
      id: '1',
      name: 'قميص قطني أزرق',
      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400',
      quantity: 2,
      price: 149.99,
      size: 'L',
      color: 'أزرق'
    }
  ]
}

const OrderConfirmationPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>()

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'تأكيد الطلب', current: true }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning" size="sm">قيد المعالجة</Badge>
      case 'confirmed':
        return <Badge variant="info" size="sm">مؤكد</Badge>
      case 'shipped':
        return <Badge variant="primary" size="sm">تم الشحن</Badge>
      case 'delivered':
        return <Badge variant="success" size="sm">تم التسليم</Badge>
      case 'cancelled':
        return <Badge variant="error" size="sm">ملغي</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  const handleDownloadInvoice = () => {
    // In a real app, this would generate and download a PDF invoice
    console.log('Download invoice for order:', orderId)
  }

  const handleTrackOrder = () => {
    window.location.href = `/orders/${orderId}/track`
  }

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <SuccessHeader>
        <SuccessIcon>
          <CheckCircle size={80} color="#10B981" />
        </SuccessIcon>
        <SuccessTitle>تم تأكيد طلبك بنجاح!</SuccessTitle>
        <SuccessMessage>
          شكراً لك على طلبك. سيتم إرسال تفاصيل الطلب إلى بريدك الإلكتروني.
          يمكنك تتبع حالة طلبك من خلال الرابط أدناه.
        </SuccessMessage>
      </SuccessHeader>

      <OrderDetails padding="lg">
        <OrderHeader>
          <OrderInfo>
            <OrderNumber>
              <Package size={24} />
              طلب رقم {mockOrder.id}
            </OrderNumber>
            <OrderMeta>
              <div>تاريخ الطلب: {formatDate(mockOrder.date)}</div>
              <div>التسليم المتوقع: {formatDate(mockOrder.estimatedDelivery)}</div>
            </OrderMeta>
          </OrderInfo>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', alignItems: 'flex-end' }}>
            {getStatusBadge(mockOrder.status)}
            <OrderActions>
              <Button variant="outline" size="sm" onClick={handleTrackOrder}>
                <Eye size={16} />
                تتبع الطلب
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownloadInvoice}>
                <Download size={16} />
                تحميل الفاتورة
              </Button>
            </OrderActions>
          </div>
        </OrderHeader>

        <InfoGrid>
          <InfoSection>
            <InfoLabel>عنوان الشحن</InfoLabel>
            <InfoValue>
              {mockOrder.shippingAddress.name}<br />
              {mockOrder.shippingAddress.address}<br />
              {mockOrder.shippingAddress.city}, {mockOrder.shippingAddress.postalCode}<br />
              {mockOrder.shippingAddress.phone}
            </InfoValue>
          </InfoSection>

          <InfoSection>
            <InfoLabel>طريقة الدفع</InfoLabel>
            <InfoValue>{mockOrder.paymentMethod}</InfoValue>
          </InfoSection>
        </InfoGrid>

        <SectionTitle>تفاصيل الطلب</SectionTitle>
        <OrderItems>
          {mockOrder.items.map((item) => (
            <OrderItem key={item.id}>
              <ItemImage src={item.image} alt={item.name} />
              <ItemInfo>
                <ItemName>{item.name}</ItemName>
                <ItemDetails>
                  الكمية: {item.quantity} | المقاس: {item.size} | اللون: {item.color}
                </ItemDetails>
              </ItemInfo>
              <ItemPrice>
                <ItemTotal>{formatPrice(item.price * item.quantity)}</ItemTotal>
              </ItemPrice>
            </OrderItem>
          ))}
        </OrderItems>

        <OrderSummary>
          <SummaryRow>
            <span>المجموع الفرعي:</span>
            <span>{formatPrice(mockOrder.subtotal)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>الشحن:</span>
            <span>{formatPrice(mockOrder.shipping)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>الضريبة (15%):</span>
            <span>{formatPrice(mockOrder.tax)}</span>
          </SummaryRow>
          <SummaryRow>
            <span>المجموع الكلي:</span>
            <span>{formatPrice(mockOrder.total)}</span>
          </SummaryRow>
        </OrderSummary>
      </OrderDetails>

      <ActionButtons>
        <Button variant="primary" onClick={() => window.location.href = '/orders'}>
          عرض جميع طلباتي
        </Button>
        <Button variant="outline" onClick={() => window.location.href = '/products'}>
          <ArrowLeft size={16} />
          متابعة التسوق
        </Button>
      </ActionButtons>
    </Container>
  )
}

export default OrderConfirmationPage
