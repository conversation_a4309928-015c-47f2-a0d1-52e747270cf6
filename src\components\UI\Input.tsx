import React, { forwardRef } from 'react'
import styled, { css } from 'styled-components'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  fullWidth?: boolean
  variant?: 'default' | 'filled'
}

const InputContainer = styled.div<{ $fullWidth: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  
  ${({ $fullWidth }) =>
    $fullWidth &&
    css`
      width: 100%;
    `}
`

const Label = styled.label`
  font-size: 0.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const StyledInput = styled.input<{
  $hasError: boolean
  $variant: string
}>`
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-family: ${({ theme }) => theme.fonts.primary};
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  transition: all ${({ theme }) => theme.transitions.fast};
  direction: rtl;
  text-align: right;
  
  ${({ $variant, theme }) =>
    $variant === 'filled' &&
    css`
      background-color: ${theme.colors.surface};
    `}
  
  &::placeholder {
    color: ${({ theme }) => theme.colors.text.light};
    direction: rtl;
    text-align: right;
  }
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary}20;
  }
  
  &:disabled {
    background-color: ${({ theme }) => theme.colors.surface};
    color: ${({ theme }) => theme.colors.text.light};
    cursor: not-allowed;
  }
  
  ${({ $hasError, theme }) =>
    $hasError &&
    css`
      border-color: ${theme.colors.status.error};
      
      &:focus {
        border-color: ${theme.colors.status.error};
        box-shadow: 0 0 0 3px ${theme.colors.status.error}20;
      }
    `}
`

const ErrorMessage = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.status.error};
  margin-top: ${({ theme }) => theme.spacing.xs};
`

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, fullWidth = false, variant = 'default', className, ...props }, ref) => {
    return (
      <InputContainer $fullWidth={fullWidth} className={className}>
        {label && <Label>{label}</Label>}
        <StyledInput
          ref={ref}
          $hasError={!!error}
          $variant={variant}
          {...props}
        />
        {error && <ErrorMessage>{error}</ErrorMessage>}
      </InputContainer>
    )
  }
)

Input.displayName = 'Input'

export default Input
