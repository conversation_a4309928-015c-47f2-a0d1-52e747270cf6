import React from 'react'
import styled from 'styled-components'
import { Check } from 'lucide-react'

interface StepperProps {
  steps: {
    title: string
    completed: boolean
    active: boolean
  }[]
  className?: string
}

const StepperContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: ${({ theme }) => theme.spacing.lg} 0;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
  }
`

const StepWrapper = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  
  &:last-child {
    flex: none;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    width: 100%;
    
    &:last-child {
      flex: 1;
    }
  }
`

const StepContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  position: relative;
  z-index: 2;
`

const StepCircle = styled.div<{ $completed: boolean; $active: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  font-size: 0.875rem;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  ${({ theme, $completed, $active }) => {
    if ($completed) {
      return `
        background-color: ${theme.colors.status.success};
        color: white;
        border: 2px solid ${theme.colors.status.success};
      `
    }
    if ($active) {
      return `
        background-color: ${theme.colors.primary};
        color: white;
        border: 2px solid ${theme.colors.primary};
      `
    }
    return `
      background-color: ${theme.colors.background};
      color: ${theme.colors.text.secondary};
      border: 2px solid ${theme.colors.border.medium};
    `
  }}
`

const StepTitle = styled.span<{ $completed: boolean; $active: boolean }>`
  font-size: 0.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  text-align: center;
  white-space: nowrap;
  transition: color ${({ theme }) => theme.transitions.fast};
  
  color: ${({ theme, $completed, $active }) => {
    if ($completed || $active) return theme.colors.text.primary
    return theme.colors.text.secondary
  }};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    white-space: normal;
    max-width: 120px;
  }
`

const StepConnector = styled.div<{ $completed: boolean }>`
  flex: 1;
  height: 2px;
  margin: 0 ${({ theme }) => theme.spacing.md};
  transition: background-color ${({ theme }) => theme.transitions.fast};
  
  background-color: ${({ theme, $completed }) => 
    $completed ? theme.colors.status.success : theme.colors.border.medium};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    width: 2px;
    height: 40px;
    margin: ${({ theme }) => theme.spacing.sm} 0;
  }
`

const Stepper: React.FC<StepperProps> = ({ steps, className }) => {
  return (
    <StepperContainer className={className}>
      {steps.map((step, index) => (
        <React.Fragment key={index}>
          <StepWrapper>
            <StepContent>
              <StepCircle $completed={step.completed} $active={step.active}>
                {step.completed ? (
                  <Check size={20} />
                ) : (
                  <span>{index + 1}</span>
                )}
              </StepCircle>
              <StepTitle $completed={step.completed} $active={step.active}>
                {step.title}
              </StepTitle>
            </StepContent>
          </StepWrapper>
          
          {index < steps.length - 1 && (
            <StepConnector $completed={step.completed} />
          )}
        </React.Fragment>
      ))}
    </StepperContainer>
  )
}

export default Stepper
