import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: false, // Disable sourcemaps in production for smaller bundle
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor libraries into separate chunks
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['styled-components', 'lucide-react'],
          utils: ['date-fns']
        }
      }
    },
    chunkSizeWarningLimit: 1000 // Increase chunk size warning limit
  },
  css: {
    devSourcemap: true
  },
  // PWA and performance optimizations
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
  }
})
