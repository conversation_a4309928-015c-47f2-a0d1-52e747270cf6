import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import {
  BarChart3,
  Package,
  ShoppingCart,
  Users,
  TrendingUp,
  Settings,
  Plus,
  Download,
  DollarSign,
  AlertTriangle
} from 'lucide-react'
import { formatPrice } from '../../utils/formatters'
import { useAdmin } from '../../context/AdminContext'
import { useAuth } from '../../context/AuthContext'
import Card from '../../components/UI/Card'
import Button from '../../components/UI/Button'
import LoadingSpinner from '../../components/UI/LoadingSpinner'

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  direction: rtl;
  min-height: 100vh;
  background: #f8f9fa;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: bold;
  color: #1a1a1a;
  margin: 0;
`

const HeaderActions = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`

const StatCard = styled(Card)`
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`

const StatIcon = styled.div<{ $color: string }>`
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: ${({ $color }) => $color}20;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ $color }) => $color};
`

const StatContent = styled.div`
  flex: 1;
`

const StatValue = styled.div`
  font-size: 1.75rem;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 0.25rem;
`

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
`

const StatChange = styled.div<{ $positive: boolean }>`
  font-size: 0.75rem;
  color: ${({ $positive }) => $positive ? '#10b981' : '#ef4444'};
  display: flex;
  align-items: center;
  gap: 0.25rem;
`

const TabsContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
  padding-bottom: 0;
`

const Tab = styled.button<{ $active: boolean }>`
  padding: 1rem 1.5rem;
  border: none;
  background: ${({ $active }) => $active ? '#3b82f6' : 'transparent'};
  color: ${({ $active }) => $active ? 'white' : '#6b7280'};
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  font-weight: 500;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    background: ${({ $active }) => $active ? '#3b82f6' : '#f3f4f6'};
    color: ${({ $active }) => $active ? 'white' : '#374151'};
  }
`

const ContentArea = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid #e5e7eb;
`

const WelcomeSection = styled.div`
  text-align: center;
  padding: 3rem 2rem;
`

const WelcomeTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 1rem;
`

const WelcomeText = styled.p`
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
`

type AdminTab = 'dashboard' | 'products' | 'orders' | 'customers' | 'analytics' | 'settings'

interface AdminTabConfig {
  id: AdminTab
  label: string
  icon: React.ComponentType<{ size?: number }>
  description: string
}

const adminTabs: AdminTabConfig[] = [
  {
    id: 'dashboard',
    label: 'لوحة التحكم',
    icon: BarChart3,
    description: 'نظرة عامة على إحصائيات المتجر'
  },
  {
    id: 'products',
    label: 'المنتجات',
    icon: Package,
    description: 'إدارة المنتجات والمخزون'
  },
  {
    id: 'orders',
    label: 'الطلبات',
    icon: ShoppingCart,
    description: 'متابعة ومعالجة الطلبات'
  },
  {
    id: 'customers',
    label: 'العملاء',
    icon: Users,
    description: 'إدارة بيانات العملاء'
  },
  {
    id: 'analytics',
    label: 'التحليلات',
    icon: TrendingUp,
    description: 'تقارير وإحصائيات مفصلة'
  },
  {
    id: 'settings',
    label: 'الإعدادات',
    icon: Settings,
    description: 'إعدادات المتجر والنظام'
  }
]

const AdminDashboard: React.FC = () => {
  const { getStatistics } = useAdmin()
  const { user, isAuthenticated } = useAuth()
  const [activeTab, setActiveTab] = useState<AdminTab>('dashboard')
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState<any>(null)

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true)
        const statistics = await getStatistics()
        setStats(statistics)
      } catch (error) {
        console.error('Error loading statistics:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [getStatistics])

  // التحقق من صلاحيات المدير
  if (!isAuthenticated || !user || user.role !== 'store_owner') {
    return (
      <Container>
        <Card>
          <div style={{ textAlign: 'center', padding: '3rem 2rem' }}>
            <AlertTriangle size={64} style={{ color: '#ef4444', margin: '0 auto 1rem' }} />
            <h2 style={{ color: '#ef4444', marginBottom: '1rem' }}>غير مصرح بالوصول</h2>
            <p style={{ color: '#6b7280', marginBottom: '2rem' }}>
              هذه الصفحة مخصصة للمديرين فقط. يرجى تسجيل الدخول بحساب إداري للوصول إلى لوحة التحكم.
            </p>
            <Button variant="primary" onClick={() => window.location.href = '/login'}>
              تسجيل الدخول
            </Button>
          </div>
        </Card>
      </Container>
    )
  }

  if (isLoading) {
    return (
      <Container>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <LoadingSpinner />
        </div>
      </Container>
    )
  }

  const handleTabChange = (tabId: AdminTab) => {
    setActiveTab(tabId)
  }

  const renderDashboardContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <WelcomeSection>
            <WelcomeTitle>مرحباً بك في لوحة التحكم</WelcomeTitle>
            <WelcomeText>
              هنا يمكنك إدارة متجرك بالكامل، متابعة المبيعات، إدارة المنتجات والطلبات، 
              ومراقبة أداء المتجر من خلال التقارير والإحصائيات المفصلة.
            </WelcomeText>
          </WelcomeSection>
        )
      case 'products':
        return (
          <WelcomeSection>
            <Package size={64} style={{ color: '#3b82f6', margin: '0 auto 1rem' }} />
            <WelcomeTitle>إدارة المنتجات</WelcomeTitle>
            <WelcomeText>
              قم بإضافة وتعديل وحذف المنتجات، إدارة المخزون، وتنظيم الفئات.
            </WelcomeText>
          </WelcomeSection>
        )
      case 'orders':
        return (
          <WelcomeSection>
            <ShoppingCart size={64} style={{ color: '#10b981', margin: '0 auto 1rem' }} />
            <WelcomeTitle>إدارة الطلبات</WelcomeTitle>
            <WelcomeText>
              متابعة الطلبات الجديدة، تحديث حالة الطلبات، وإدارة عمليات الشحن والتسليم.
            </WelcomeText>
          </WelcomeSection>
        )
      case 'customers':
        return (
          <WelcomeSection>
            <Users size={64} style={{ color: '#8b5cf6', margin: '0 auto 1rem' }} />
            <WelcomeTitle>إدارة العملاء</WelcomeTitle>
            <WelcomeText>
              عرض بيانات العملاء، متابعة تاريخ المشتريات، وإدارة خدمة العملاء.
            </WelcomeText>
          </WelcomeSection>
        )
      case 'analytics':
        return (
          <WelcomeSection>
            <TrendingUp size={64} style={{ color: '#f59e0b', margin: '0 auto 1rem' }} />
            <WelcomeTitle>التحليلات والتقارير</WelcomeTitle>
            <WelcomeText>
              تقارير مفصلة عن المبيعات، أداء المنتجات، وسلوك العملاء لاتخاذ قرارات أفضل.
            </WelcomeText>
          </WelcomeSection>
        )
      case 'settings':
        return (
          <WelcomeSection>
            <Settings size={64} style={{ color: '#6b7280', margin: '0 auto 1rem' }} />
            <WelcomeTitle>إعدادات المتجر</WelcomeTitle>
            <WelcomeText>
              إعدادات عامة للمتجر، طرق الدفع، الشحن، والإشعارات.
            </WelcomeText>
          </WelcomeSection>
        )
      default:
        return null
    }
  }

  return (
    <Container>
      <Header>
        <Title>لوحة التحكم الإدارية</Title>
        <HeaderActions>
          <Button variant="outline">
            <Download size={16} />
            تصدير التقارير
          </Button>
          <Button variant="primary">
            <Plus size={16} />
            إضافة منتج جديد
          </Button>
        </HeaderActions>
      </Header>

      {/* إحصائيات سريعة */}
      <StatsGrid>
        <StatCard>
          <StatIcon $color="#3b82f6">
            <DollarSign size={24} />
          </StatIcon>
          <StatContent>
            <StatValue>{formatPrice(stats?.totalRevenue || 0)}</StatValue>
            <StatLabel>إجمالي المبيعات</StatLabel>
            <StatChange $positive={true}>
              <TrendingUp size={12} />
              +12.5% من الشهر الماضي
            </StatChange>
          </StatContent>
        </StatCard>

        <StatCard>
          <StatIcon $color="#10b981">
            <ShoppingCart size={24} />
          </StatIcon>
          <StatContent>
            <StatValue>{stats?.totalOrders || 0}</StatValue>
            <StatLabel>إجمالي الطلبات</StatLabel>
            <StatChange $positive={true}>
              <TrendingUp size={12} />
              +8.2% من الشهر الماضي
            </StatChange>
          </StatContent>
        </StatCard>

        <StatCard>
          <StatIcon $color="#8b5cf6">
            <Users size={24} />
          </StatIcon>
          <StatContent>
            <StatValue>{stats?.totalCustomers || 0}</StatValue>
            <StatLabel>إجمالي العملاء</StatLabel>
            <StatChange $positive={true}>
              <TrendingUp size={12} />
              +15.3% من الشهر الماضي
            </StatChange>
          </StatContent>
        </StatCard>

        <StatCard>
          <StatIcon $color="#f59e0b">
            <Package size={24} />
          </StatIcon>
          <StatContent>
            <StatValue>{stats?.totalProducts || 0}</StatValue>
            <StatLabel>إجمالي المنتجات</StatLabel>
            <StatChange $positive={false}>
              <TrendingUp size={12} />
              -2.1% من الشهر الماضي
            </StatChange>
          </StatContent>
        </StatCard>
      </StatsGrid>

      {/* التبويبات */}
      <TabsContainer>
        {adminTabs.map((tab) => {
          const IconComponent = tab.icon
          return (
            <Tab
              key={tab.id}
              $active={activeTab === tab.id}
              onClick={() => handleTabChange(tab.id)}
            >
              <IconComponent size={18} />
              {tab.label}
            </Tab>
          )
        })}
      </TabsContainer>

      {/* محتوى التبويب */}
      <ContentArea>
        {renderDashboardContent()}
      </ContentArea>
    </Container>
  )
}

export default AdminDashboard
