import React, { useState } from 'react'
import styled from 'styled-components'
import {
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Percent,
  Calendar,
  Tag,
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Copy
} from 'lucide-react'
import { formatPrice, formatDate, formatPercentage } from '../../utils/formatters'
import Card from '../../components/UI/Card'
import Button from '../../components/UI/Button'
import Input from '../../components/UI/Input'
import Select from '../../components/UI/Select'
import Badge from '../../components/UI/Badge'
import Modal from '../../components/UI/Modal'

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    justify-content: stretch;
  }
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const StatCard = styled(Card)`
  text-align: center;
`

const StatIcon = styled.div<{ $color: string }>`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ $color }) => $color}20;
  color: ${({ $color }) => $color};
  margin: 0 auto ${({ theme }) => theme.spacing.md};
`

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const FiltersSection = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: end;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`

const DiscountsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
`

const DiscountCard = styled(Card)`
  position: relative;
  transition: all ${({ theme }) => theme.transitions.medium};

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`

const DiscountHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const DiscountInfo = styled.div`
  flex: 1;
`

const DiscountTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
`

const DiscountCode = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  font-family: monospace;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  background-color: ${({ theme }) => theme.colors.surface};
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  width: fit-content;
`

const DiscountActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ActionButton = styled(Button)`
  padding: ${({ theme }) => theme.spacing.xs};
  min-width: auto;
`

const AdminDiscounts: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  // Mock data
  const mockDiscountStats = {
    totalDiscounts: 15,
    activeDiscounts: 8,
    totalSavings: 45230,
    totalUsage: 342
  }

  const mockDiscounts = [
    {
      id: '1',
      title: 'خصم الجمعة البيضاء',
      code: 'BLACKFRIDAY2024',
      type: 'percentage',
      value: 25,
      minAmount: 200,
      usageCount: 45,
      usageLimit: 100,
      status: 'active'
    },
    {
      id: '2',
      title: 'خصم العملاء الجدد',
      code: 'WELCOME20',
      type: 'percentage',
      value: 20,
      minAmount: 150,
      usageCount: 89,
      usageLimit: null,
      status: 'active'
    },
    {
      id: '3',
      title: 'خصم ثابت 50 ريال',
      code: 'SAVE50',
      type: 'fixed',
      value: 50,
      minAmount: 300,
      usageCount: 23,
      usageLimit: 50,
      status: 'active'
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success" size="sm">نشط</Badge>
      case 'paused':
        return <Badge variant="warning" size="sm">متوقف</Badge>
      case 'expired':
        return <Badge variant="error" size="sm">منتهي</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  const filteredDiscounts = mockDiscounts.filter(discount => {
    const matchesSearch = discount.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         discount.code.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || discount.status === statusFilter
    const matchesType = !typeFilter || discount.type === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  return (
    <Container>
      <Header>
        <Title>إدارة الخصومات</Title>
        <HeaderActions>
          <Button variant="primary" onClick={() => setShowAddModal(true)}>
            <Plus size={16} />
            إضافة خصم جديد
          </Button>
        </HeaderActions>
      </Header>

      <StatsGrid>
        <StatCard padding="lg">
          <StatIcon $color="#3B82F6">
            <Tag size={24} />
          </StatIcon>
          <StatValue>{mockDiscountStats.totalDiscounts}</StatValue>
          <StatLabel>إجمالي الخصومات</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#10B981">
            <CheckCircle size={24} />
          </StatIcon>
          <StatValue>{mockDiscountStats.activeDiscounts}</StatValue>
          <StatLabel>خصومات نشطة</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#F59E0B">
            <DollarSign size={24} />
          </StatIcon>
          <StatValue>{formatPrice(mockDiscountStats.totalSavings)} ر.س</StatValue>
          <StatLabel>إجمالي التوفير</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#8B5CF6">
            <Users size={24} />
          </StatIcon>
          <StatValue>{mockDiscountStats.totalUsage}</StatValue>
          <StatLabel>مرات الاستخدام</StatLabel>
        </StatCard>
      </StatsGrid>

      <FiltersSection padding="lg">
        <FiltersGrid>
          <Input
            type="text"
            placeholder="البحث في الخصومات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<Search size={16} />}
          />

          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع الحالات' },
              { value: 'active', label: 'نشط' },
              { value: 'paused', label: 'متوقف' },
              { value: 'expired', label: 'منتهي' }
            ]}
          />

          <Select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع الأنواع' },
              { value: 'percentage', label: 'نسبة مئوية' },
              { value: 'fixed', label: 'مبلغ ثابت' },
              { value: 'shipping', label: 'شحن مجاني' }
            ]}
          />

          <Button variant="outline">
            <Filter size={16} />
            تصفية متقدمة
          </Button>
        </FiltersGrid>
      </FiltersSection>

      <DiscountsGrid>
        {filteredDiscounts.map((discount) => (
          <DiscountCard key={discount.id} padding="lg">
            <DiscountHeader>
              <DiscountInfo>
                <DiscountTitle>{discount.title}</DiscountTitle>
                {getStatusBadge(discount.status)}
              </DiscountInfo>
              <DiscountActions>
                <ActionButton variant="outline" size="sm">
                  <Eye size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Edit size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Trash2 size={14} />
                </ActionButton>
              </DiscountActions>
            </DiscountHeader>

            <DiscountCode>
              <Tag size={14} />
              {discount.code}
              <ActionButton variant="outline" size="sm" onClick={() => navigator.clipboard.writeText(discount.code)}>
                <Copy size={12} />
              </ActionButton>
            </DiscountCode>

            <div style={{
              textAlign: 'center',
              padding: '1rem',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              borderRadius: '8px',
              marginBottom: '1rem'
            }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                {discount.type === 'percentage' ? `${discount.value}%` :
                 discount.type === 'fixed' ? `${discount.value} ر.س` :
                 'شحن مجاني'}
              </div>
              <div style={{ fontSize: '0.875rem', opacity: 0.9 }}>
                {discount.type === 'percentage' ? 'خصم نسبي' :
                 discount.type === 'fixed' ? 'خصم ثابت' :
                 'شحن مجاني'}
              </div>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', marginBottom: '1rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.875rem', color: '#6B7280' }}>
                <DollarSign size={14} />
                الحد الأدنى: {formatPrice(discount.minAmount)} ر.س
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.875rem', color: '#6B7280' }}>
                <Users size={14} />
                الاستخدام: {discount.usageCount} / {discount.usageLimit || '∞'}
              </div>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '1rem',
              paddingTop: '1rem',
              borderTop: '1px solid #E5E7EB'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#111827', marginBottom: '0.25rem' }}>
                  {discount.usageCount}
                </div>
                <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>
                  مرات الاستخدام
                </div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '1.125rem', fontWeight: 'bold', color: '#111827', marginBottom: '0.25rem' }}>
                  {discount.usageLimit ? Math.round((discount.usageCount / discount.usageLimit) * 100) : 0}%
                </div>
                <div style={{ fontSize: '0.75rem', color: '#6B7280' }}>
                  نسبة الاستخدام
                </div>
              </div>
            </div>
          </DiscountCard>
        ))}
      </DiscountsGrid>

      {showAddModal && (
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="إضافة خصم جديد"
        >
          <div style={{ padding: '1rem' }}>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                عنوان الخصم
              </label>
              <Input
                type="text"
                placeholder="أدخل عنوان الخصم..."
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                كود الخصم
              </label>
              <Input
                type="text"
                placeholder="أدخل كود الخصم..."
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                نوع الخصم
              </label>
              <Select
                value="percentage"
                onChange={() => {}}
                options={[
                  { value: 'percentage', label: 'نسبة مئوية' },
                  { value: 'fixed', label: 'مبلغ ثابت' },
                  { value: 'shipping', label: 'شحن مجاني' }
                ]}
              />
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', marginTop: '2rem' }}>
              <Button variant="outline" onClick={() => setShowAddModal(false)}>
                إلغاء
              </Button>
              <Button variant="primary">
                حفظ الخصم
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </Container>
  )
}

export default AdminDiscounts
