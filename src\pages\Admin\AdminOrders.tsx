import React, { useState } from 'react'
import styled from 'styled-components'
import {
  Search,
  Filter,
  Eye,
  Edit,
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Download,
  RefreshCw
} from 'lucide-react'
import { formatPrice, formatDate, formatDateTime } from '../../utils/formatters'
import Card from '../../components/UI/Card'
import Button from '../../components/UI/Button'
import Input from '../../components/UI/Input'
import Select from '../../components/UI/Select'
import Badge from '../../components/UI/Badge'
import Modal from '../../components/UI/Modal'

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    justify-content: stretch;
  }
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const StatCard = styled(Card)`
  text-align: center;
`

const StatIcon = styled.div<{ $color: string }>`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ $color }) => $color}20;
  color: ${({ $color }) => $color};
  margin: 0 auto ${({ theme }) => theme.spacing.md};
`

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const FiltersSection = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: end;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`

const OrdersTable = styled(Card)`
  overflow: hidden;
`

const TableHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const TableTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const Table = styled.div`
  overflow-x: auto;
`

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 120px;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  align-items: center;

  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }

  &:first-child {
    background-color: ${({ theme }) => theme.colors.surface};
    font-weight: ${({ theme }) => theme.fonts.weights.semibold};
    border-bottom: 2px solid ${({ theme }) => theme.colors.border.medium};

    &:hover {
      background-color: ${({ theme }) => theme.colors.surface};
    }
  }
`

const OrderInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const OrderId = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const OrderDate = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const CustomerInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const CustomerName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const CustomerEmail = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const OrderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  justify-content: flex-end;
`

const ActionButton = styled(Button)`
  padding: ${({ theme }) => theme.spacing.xs};
  min-width: auto;
`

// Mock data
const mockOrderStats = {
  totalOrders: 1247,
  pendingOrders: 23,
  processingOrders: 45,
  shippedOrders: 67,
  deliveredOrders: 1089,
  cancelledOrders: 23
}

const mockOrders = [
  {
    id: 'ORD-2024-001',
    customer: {
      name: 'أحمد محمد',
      email: '<EMAIL>'
    },
    date: '2024-01-15T10:30:00Z',
    total: 299.99,
    status: 'pending',
    items: 3,
    paymentMethod: 'credit_card'
  },
  {
    id: 'ORD-2024-002',
    customer: {
      name: 'فاطمة علي',
      email: '<EMAIL>'
    },
    date: '2024-01-15T09:15:00Z',
    total: 450.00,
    status: 'confirmed',
    items: 2,
    paymentMethod: 'cash_on_delivery'
  },
  {
    id: 'ORD-2024-003',
    customer: {
      name: 'محمد سالم',
      email: '<EMAIL>'
    },
    date: '2024-01-14T16:45:00Z',
    total: 199.99,
    status: 'shipped',
    items: 1,
    paymentMethod: 'credit_card'
  },
  {
    id: 'ORD-2024-004',
    customer: {
      name: 'نورا أحمد',
      email: '<EMAIL>'
    },
    date: '2024-01-14T14:20:00Z',
    total: 350.00,
    status: 'delivered',
    items: 4,
    paymentMethod: 'bank_transfer'
  },
  {
    id: 'ORD-2024-005',
    customer: {
      name: 'خالد عبدالله',
      email: '<EMAIL>'
    },
    date: '2024-01-13T11:30:00Z',
    total: 275.50,
    status: 'cancelled',
    items: 2,
    paymentMethod: 'credit_card'
  }
]

const AdminOrders: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [dateFilter, setDateFilter] = useState('')
  const [showOrderModal, setShowOrderModal] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<any>(null)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning" size="sm">قيد المعالجة</Badge>
      case 'confirmed':
        return <Badge variant="info" size="sm">مؤكد</Badge>
      case 'processing':
        return <Badge variant="primary" size="sm">قيد التحضير</Badge>
      case 'shipped':
        return <Badge variant="primary" size="sm">تم الشحن</Badge>
      case 'delivered':
        return <Badge variant="success" size="sm">تم التسليم</Badge>
      case 'cancelled':
        return <Badge variant="error" size="sm">ملغي</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} />
      case 'confirmed':
      case 'processing':
        return <Package size={16} />
      case 'shipped':
        return <Truck size={16} />
      case 'delivered':
        return <CheckCircle size={16} />
      case 'cancelled':
        return <XCircle size={16} />
      default:
        return <Clock size={16} />
    }
  }

  const filteredOrders = mockOrders.filter(order => {
    const matchesSearch = order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || order.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleViewOrder = (order: any) => {
    setSelectedOrder(order)
    setShowOrderModal(true)
  }

  return (
    <Container>
      <Header>
        <Title>إدارة الطلبات</Title>
        <HeaderActions>
          <Button variant="outline">
            <RefreshCw size={16} />
            تحديث
          </Button>
          <Button variant="outline">
            <Download size={16} />
            تصدير
          </Button>
        </HeaderActions>
      </Header>

      <StatsGrid>
        <StatCard padding="lg">
          <StatIcon $color="#3B82F6">
            <Package size={24} />
          </StatIcon>
          <StatValue>{mockOrderStats.totalOrders}</StatValue>
          <StatLabel>إجمالي الطلبات</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#F59E0B">
            <Clock size={24} />
          </StatIcon>
          <StatValue>{mockOrderStats.pendingOrders}</StatValue>
          <StatLabel>قيد المعالجة</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#8B5CF6">
            <Truck size={24} />
          </StatIcon>
          <StatValue>{mockOrderStats.shippedOrders}</StatValue>
          <StatLabel>تم الشحن</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#10B981">
            <CheckCircle size={24} />
          </StatIcon>
          <StatValue>{mockOrderStats.deliveredOrders}</StatValue>
          <StatLabel>تم التسليم</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#EF4444">
            <XCircle size={24} />
          </StatIcon>
          <StatValue>{mockOrderStats.cancelledOrders}</StatValue>
          <StatLabel>ملغية</StatLabel>
        </StatCard>
      </StatsGrid>

      <FiltersSection padding="lg">
        <FiltersGrid>
          <Input
            type="text"
            placeholder="البحث في الطلبات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<Search size={16} />}
          />

          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع الحالات' },
              { value: 'pending', label: 'قيد المعالجة' },
              { value: 'confirmed', label: 'مؤكد' },
              { value: 'processing', label: 'قيد التحضير' },
              { value: 'shipped', label: 'تم الشحن' },
              { value: 'delivered', label: 'تم التسليم' },
              { value: 'cancelled', label: 'ملغي' }
            ]}
          />

          <Select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع التواريخ' },
              { value: 'today', label: 'اليوم' },
              { value: 'week', label: 'هذا الأسبوع' },
              { value: 'month', label: 'هذا الشهر' },
              { value: 'quarter', label: 'هذا الربع' }
            ]}
          />

          <Button variant="outline">
            <Filter size={16} />
            تصفية متقدمة
          </Button>
        </FiltersGrid>
      </FiltersSection>

      <OrdersTable padding="lg">
        <TableHeader>
          <TableTitle>الطلبات ({filteredOrders.length})</TableTitle>
          <Button variant="outline" size="sm">
            تصدير البيانات
          </Button>
        </TableHeader>

        <Table>
          <TableRow>
            <div>رقم الطلب</div>
            <div>العميل</div>
            <div>المجموع</div>
            <div>العناصر</div>
            <div>طريقة الدفع</div>
            <div>الحالة</div>
            <div>الإجراءات</div>
          </TableRow>

          {filteredOrders.map((order) => (
            <TableRow key={order.id}>
              <OrderInfo>
                <OrderId>{order.id}</OrderId>
                <OrderDate>{formatDate(order.date)}</OrderDate>
              </OrderInfo>

              <CustomerInfo>
                <CustomerName>{order.customer.name}</CustomerName>
                <CustomerEmail>{order.customer.email}</CustomerEmail>
              </CustomerInfo>

              <div>{formatPrice(order.total)} ر.س</div>

              <div>{order.items} عنصر</div>

              <div>
                {order.paymentMethod === 'credit_card' && 'بطاقة ائتمان'}
                {order.paymentMethod === 'cash_on_delivery' && 'الدفع عند الاستلام'}
                {order.paymentMethod === 'bank_transfer' && 'تحويل بنكي'}
              </div>

              <div>{getStatusBadge(order.status)}</div>

              <OrderActions>
                <ActionButton variant="outline" size="sm" onClick={() => handleViewOrder(order)}>
                  <Eye size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Edit size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Download size={14} />
                </ActionButton>
              </OrderActions>
            </TableRow>
          ))}
        </Table>
      </OrdersTable>

      {showOrderModal && selectedOrder && (
        <Modal
          isOpen={showOrderModal}
          onClose={() => setShowOrderModal(false)}
          title={`تفاصيل الطلب ${selectedOrder.id}`}
        >
          <div style={{ padding: '1rem' }}>
            <div style={{ marginBottom: '1rem' }}>
              <strong>العميل:</strong> {selectedOrder.customer.name}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>البريد الإلكتروني:</strong> {selectedOrder.customer.email}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>تاريخ الطلب:</strong> {formatDateTime(selectedOrder.date)}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>المجموع:</strong> {formatPrice(selectedOrder.total)} ر.س
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>عدد العناصر:</strong> {selectedOrder.items}
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>الحالة:</strong> {getStatusBadge(selectedOrder.status)}
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', marginTop: '2rem' }}>
              <Button variant="outline" onClick={() => setShowOrderModal(false)}>
                إغلاق
              </Button>
              <Button variant="primary">
                تحديث الحالة
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </Container>
  )
}

export default AdminOrders
