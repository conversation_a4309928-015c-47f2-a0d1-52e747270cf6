// Accessibility utilities for better user experience

// Focus management utilities
export const focusManagement = {
  // Trap focus within a container (useful for modals)
  trapFocus: (container: HTMLElement): (() => void) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>
    
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]
    
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return
      
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus()
          e.preventDefault()
        }
      }
    }
    
    container.addEventListener('keydown', handleTabKey)
    firstElement?.focus()
    
    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey)
    }
  },
  
  // Save and restore focus
  saveFocus: (): (() => void) => {
    const activeElement = document.activeElement as HTMLElement
    return () => {
      if (activeElement && activeElement.focus) {
        activeElement.focus()
      }
    }
  },
  
  // Focus first error in form
  focusFirstError: (container: HTMLElement): void => {
    const errorElement = container.querySelector('[aria-invalid="true"], .error') as HTMLElement
    if (errorElement) {
      errorElement.focus()
    }
  }
}

// ARIA utilities
export const aria = {
  // Announce to screen readers
  announce: (message: string, priority: 'polite' | 'assertive' = 'polite'): void => {
    const announcer = document.createElement('div')
    announcer.setAttribute('aria-live', priority)
    announcer.setAttribute('aria-atomic', 'true')
    announcer.className = 'sr-only'
    announcer.textContent = message
    
    document.body.appendChild(announcer)
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcer)
    }, 1000)
  },
  
  // Generate unique IDs for ARIA relationships
  generateId: (prefix: string = 'aria'): string => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
  },
  
  // Set ARIA attributes safely
  setAttributes: (element: HTMLElement, attributes: Record<string, string>): void => {
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value)
    })
  }
}

// Keyboard navigation utilities
export const keyboard = {
  // Handle arrow key navigation in lists
  handleArrowNavigation: (
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onIndexChange: (index: number) => void
  ): void => {
    let newIndex = currentIndex
    
    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0
        break
      case 'ArrowUp':
      case 'ArrowLeft':
        newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1
        break
      case 'Home':
        newIndex = 0
        break
      case 'End':
        newIndex = items.length - 1
        break
      default:
        return
    }
    
    event.preventDefault()
    onIndexChange(newIndex)
    items[newIndex]?.focus()
  },
  
  // Handle escape key
  handleEscape: (callback: () => void) => (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      callback()
    }
  },
  
  // Handle enter and space for custom buttons
  handleActivation: (callback: () => void) => (event: KeyboardEvent): void => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      callback()
    }
  }
}

// Color contrast utilities
export const colorContrast = {
  // Calculate relative luminance
  getLuminance: (hex: string): number => {
    const rgb = parseInt(hex.slice(1), 16)
    const r = (rgb >> 16) & 0xff
    const g = (rgb >> 8) & 0xff
    const b = (rgb >> 0) & 0xff
    
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
    })
    
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
  },
  
  // Calculate contrast ratio
  getContrastRatio: (color1: string, color2: string): number => {
    const lum1 = colorContrast.getLuminance(color1)
    const lum2 = colorContrast.getLuminance(color2)
    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)
    
    return (brightest + 0.05) / (darkest + 0.05)
  },
  
  // Check if contrast meets WCAG standards
  meetsWCAG: (color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean => {
    const ratio = colorContrast.getContrastRatio(color1, color2)
    return level === 'AA' ? ratio >= 4.5 : ratio >= 7
  }
}

// Screen reader utilities
export const screenReader = {
  // Hide content from screen readers
  hide: (element: HTMLElement): void => {
    element.setAttribute('aria-hidden', 'true')
  },
  
  // Show content to screen readers
  show: (element: HTMLElement): void => {
    element.removeAttribute('aria-hidden')
  },
  
  // Create screen reader only text
  createSROnlyText: (text: string): HTMLElement => {
    const span = document.createElement('span')
    span.className = 'sr-only'
    span.textContent = text
    return span
  }
}

// Form accessibility utilities
export const formAccessibility = {
  // Associate label with input
  associateLabel: (input: HTMLInputElement, label: HTMLLabelElement): void => {
    const id = input.id || aria.generateId('input')
    input.id = id
    label.setAttribute('for', id)
  },
  
  // Add error message to input
  addErrorMessage: (input: HTMLInputElement, message: string): void => {
    const errorId = aria.generateId('error')
    const errorElement = document.createElement('div')
    errorElement.id = errorId
    errorElement.className = 'error-message'
    errorElement.textContent = message
    errorElement.setAttribute('role', 'alert')
    
    input.setAttribute('aria-invalid', 'true')
    input.setAttribute('aria-describedby', errorId)
    
    input.parentNode?.insertBefore(errorElement, input.nextSibling)
  },
  
  // Remove error message
  removeErrorMessage: (input: HTMLInputElement): void => {
    input.removeAttribute('aria-invalid')
    const describedBy = input.getAttribute('aria-describedby')
    if (describedBy) {
      const errorElement = document.getElementById(describedBy)
      if (errorElement) {
        errorElement.remove()
      }
      input.removeAttribute('aria-describedby')
    }
  }
}

// Motion preferences
export const motionPreferences = {
  // Check if user prefers reduced motion
  prefersReducedMotion: (): boolean => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  },
  
  // Respect motion preferences in animations
  respectMotionPreference: (element: HTMLElement, animation: string): void => {
    if (!motionPreferences.prefersReducedMotion()) {
      element.style.animation = animation
    }
  }
}

// High contrast mode detection
export const highContrast = {
  // Detect high contrast mode
  isHighContrastMode: (): boolean => {
    return window.matchMedia('(prefers-contrast: high)').matches
  },
  
  // Apply high contrast styles
  applyHighContrastStyles: (element: HTMLElement): void => {
    if (highContrast.isHighContrastMode()) {
      element.classList.add('high-contrast')
    }
  }
}

// Initialize accessibility features
export const initAccessibility = (): void => {
  // Add skip link if not present
  if (!document.querySelector('.skip-link')) {
    const skipLink = document.createElement('a')
    skipLink.href = '#main-content'
    skipLink.className = 'skip-link'
    skipLink.textContent = 'تخطي إلى المحتوى الرئيسي'
    document.body.insertBefore(skipLink, document.body.firstChild)
  }
  
  // Add focus-visible polyfill behavior
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      document.body.classList.add('keyboard-navigation')
    }
  })
  
  document.addEventListener('mousedown', () => {
    document.body.classList.remove('keyboard-navigation')
  })
}

export default {
  focusManagement,
  aria,
  keyboard,
  colorContrast,
  screenReader,
  formAccessibility,
  motionPreferences,
  highContrast,
  initAccessibility
}
