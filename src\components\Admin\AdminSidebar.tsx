import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import styled from 'styled-components'
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  BarChart3,
  Tag,
  Percent,
  Settings,
  LogOut,
  ChevronRight
} from 'lucide-react'
import { useAuth } from '../../context/AuthContext'

const SidebarContainer = styled.aside<{ isOpen?: boolean }>`
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #1a1a1a 0%, #000 100%);
  border-left: 1px solid #333;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    width: 260px;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    transform: translateX(${props => props.isOpen ? '0' : '100%'});
    transition: transform 0.3s ease;
  }
`

const SidebarHeader = styled.div`
  padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.lg};
  border-bottom: 1px solid #333;
  text-align: center;
`

const Logo = styled.h1`
  color: white;
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  margin: 0;
  font-family: 'Cairo', sans-serif;
`

const Subtitle = styled.p`
  color: #888;
  font-size: 0.875rem;
  margin: ${({ theme }) => theme.spacing.xs} 0 0;
`

const NavSection = styled.div`
  padding: ${({ theme }) => theme.spacing.lg} 0;
`

const SectionTitle = styled.h3`
  color: #666;
  font-size: 0.75rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 ${({ theme }) => theme.spacing.md};
  padding: 0 ${({ theme }) => theme.spacing.lg};
`

const NavList = styled.ul`
  list-style: none;
  margin: 0;
  padding: 0;
`

const NavItem = styled.li`
  margin: 0;
`

const NavLink = styled(Link)<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};
  color: ${props => props.$isActive ? '#fff' : '#ccc'};
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    color: white;
  }
  
  ${props => props.$isActive && `
    background: rgba(255, 255, 255, 0.1);
    border-left: 3px solid #fff;
    
    &::before {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: white;
    }
  `}
`

const NavIcon = styled.div`
  margin-left: ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
  
  svg {
    width: 20px;
    height: 20px;
  }
`

const NavText = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  font-size: 0.9rem;
`

const NavArrow = styled.div`
  margin-right: auto;
  opacity: 0.5;
  transition: opacity 0.2s ease;
  
  ${NavLink}:hover & {
    opacity: 1;
  }
`

const SidebarFooter = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: ${({ theme }) => theme.spacing.lg};
  border-top: 1px solid #333;
`

const LogoutButton = styled.button`
  width: 100%;
  display: flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.md};
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
  }
  
  svg {
    margin-left: ${({ theme }) => theme.spacing.sm};
  }
`

interface AdminSidebarProps {
  isOpen?: boolean
  onClose?: () => void
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen = true, onClose }) => {
  const location = useLocation()
  const { logout } = useAuth()

  const isActivePath = (path: string) => {
    if (path === '/admin') {
      return location.pathname === '/admin'
    }
    return location.pathname.startsWith(path)
  }

  const handleLogout = () => {
    logout()
  }

  const navItems = [
    {
      section: 'الرئيسية',
      items: [
        { path: '/admin', icon: LayoutDashboard, label: 'لوحة التحكم' }
      ]
    },
    {
      section: 'إدارة المتجر',
      items: [
        { path: '/admin/products', icon: Package, label: 'المنتجات' },
        { path: '/admin/categories', icon: Tag, label: 'الفئات' },
        { path: '/admin/orders', icon: ShoppingCart, label: 'الطلبات' },
        { path: '/admin/customers', icon: Users, label: 'العملاء' }
      ]
    },
    {
      section: 'التسويق والتحليلات',
      items: [
        { path: '/admin/discounts', icon: Percent, label: 'الخصومات' },
        { path: '/admin/analytics', icon: BarChart3, label: 'التقارير' }
      ]
    }
  ]

  return (
    <SidebarContainer isOpen={isOpen}>
      <SidebarHeader>
        <Logo>لوحة الإدارة</Logo>
        <Subtitle>إدارة متجر الملابس</Subtitle>
      </SidebarHeader>

      {navItems.map((section, index) => (
        <NavSection key={index}>
          <SectionTitle>{section.section}</SectionTitle>
          <NavList>
            {section.items.map((item) => (
              <NavItem key={item.path}>
                <NavLink 
                  to={item.path} 
                  $isActive={isActivePath(item.path)}
                  onClick={onClose}
                >
                  <NavIcon>
                    <item.icon />
                  </NavIcon>
                  <NavText>{item.label}</NavText>
                  <NavArrow>
                    <ChevronRight size={16} />
                  </NavArrow>
                </NavLink>
              </NavItem>
            ))}
          </NavList>
        </NavSection>
      ))}

      <SidebarFooter>
        <LogoutButton onClick={handleLogout}>
          <LogOut size={18} />
          تسجيل الخروج
        </LogoutButton>
      </SidebarFooter>
    </SidebarContainer>
  )
}

export default AdminSidebar
