import React from 'react'
import styled from 'styled-components'
import { useCart } from '../context/CartContext'
import CartItem from '../components/Cart/CartItem'
import CartSummary from '../components/Cart/CartSummary'
import Breadcrumb from '../components/UI/Breadcrumb'
import Button from '../components/UI/Button'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.primary};
`

const CartContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing.lg};
  }
`

const CartItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`

const EmptyCart = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.secondary};
`

const EmptyCartTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const EmptyCartText = styled.p`
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const CartActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const ItemCount = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const CartPage: React.FC = () => {
  const { items, clearCart, itemCount } = useCart()

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'سلة التسوق', current: true }
  ]

  if (items.length === 0) {
    return (
      <Container>
        <BreadcrumbWrapper>
          <Breadcrumb items={breadcrumbItems} />
        </BreadcrumbWrapper>

        <Title>سلة التسوق</Title>

        <EmptyCart>
          <EmptyCartTitle>سلة التسوق فارغة</EmptyCartTitle>
          <EmptyCartText>
            لم تقم بإضافة أي منتجات إلى سلة التسوق بعد.
            تصفح مجموعتنا الواسعة من المنتجات واختر ما يناسبك.
          </EmptyCartText>
          <Button variant="primary" size="lg" onClick={() => window.location.href = '/products'}>
            تصفح المنتجات
          </Button>
        </EmptyCart>
      </Container>
    )
  }

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <Title>سلة التسوق</Title>

      <CartActions>
        <ItemCount>
          {itemCount} {itemCount === 1 ? 'منتج' : 'منتجات'}
        </ItemCount>
        <Button variant="ghost" size="sm" onClick={clearCart}>
          إفراغ السلة
        </Button>
      </CartActions>

      <CartContent>
        <CartItems>
          {items.map((item) => (
            <CartItem key={`${item.id}-${item.size}-${item.color}`} item={item} />
          ))}
        </CartItems>

        <CartSummary />
      </CartContent>
    </Container>
  )
}

export default CartPage
