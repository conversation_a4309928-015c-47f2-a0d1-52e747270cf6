import React from 'react'
import styled from 'styled-components'
import { Package, Truck, CheckCircle, Clock, MapPin, Phone, Mail } from 'lucide-react'
import { useParams } from 'react-router-dom'
import { formatDate, formatTime } from '../utils/formatters'
import Breadcrumb from '../components/UI/Breadcrumb'
import Card from '../components/UI/Card'
import Badge from '../components/UI/Badge'
import Button from '../components/UI/Button'

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const OrderHeader = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const HeaderContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
  }
`

const OrderInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`

const OrderNumber = styled.h1`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`

const OrderMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const TrackingProgress = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const ProgressTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
`

const ProgressSteps = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`

const ProgressStep = styled.div<{ $completed: boolean; $active: boolean }>`
  display: flex;
  align-items: flex-start;
  gap: ${({ theme }) => theme.spacing.md};
  position: relative;
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 19px;
    top: 40px;
    width: 2px;
    height: calc(100% + ${({ theme }) => theme.spacing.lg});
    background-color: ${({ theme, $completed }) => 
      $completed ? theme.colors.status.success : theme.colors.border.medium};
  }
`

const StepIcon = styled.div<{ $completed: boolean; $active: boolean }>`
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme, $completed, $active }) => {
    if ($completed) return theme.colors.status.success
    if ($active) return theme.colors.primary
    return theme.colors.border.medium
  }};
  color: ${({ theme, $completed, $active }) => 
    $completed || $active ? 'white' : theme.colors.text.secondary};
  flex-shrink: 0;
`

const StepContent = styled.div`
  flex: 1;
  padding-top: ${({ theme }) => theme.spacing.xs};
`

const StepTitle = styled.h3<{ $completed: boolean; $active: boolean }>`
  font-size: 1rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme, $completed, $active }) => {
    if ($completed || $active) return theme.colors.text.primary
    return theme.colors.text.secondary
  }};
  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
`

const StepDescription = styled.p`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
  line-height: 1.5;
`

const StepTime = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.tertiary};
`

const DeliveryInfo = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`

const InfoSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`

const InfoLabel = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const InfoValue = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.5;
`

const ContactInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-top: ${({ theme }) => theme.spacing.sm};
`

const ContactButton = styled(Button)`
  padding: ${({ theme }) => theme.spacing.sm};
  min-width: auto;
`

// Mock tracking data
const mockTracking = {
  orderId: 'ORD-2024-001',
  orderDate: '2024-01-15T10:30:00Z',
  estimatedDelivery: '2024-01-20',
  currentStatus: 'shipped',
  trackingNumber: 'TRK123456789',
  carrier: 'شركة الشحن السريع',
  carrierPhone: '+966112345678',
  carrierEmail: '<EMAIL>',
  deliveryAddress: {
    name: 'أحمد محمد',
    address: 'شارع الملك فهد، حي النخيل',
    city: 'الرياض',
    postalCode: '12345',
    phone: '+966501234567'
  },
  steps: [
    {
      id: 'ordered',
      title: 'تم تأكيد الطلب',
      description: 'تم استلام طلبك وتأكيده بنجاح',
      timestamp: '2024-01-15T10:30:00Z',
      completed: true,
      active: false,
      icon: CheckCircle
    },
    {
      id: 'processing',
      title: 'قيد التحضير',
      description: 'يتم تحضير طلبك وتجهيزه للشحن',
      timestamp: '2024-01-15T14:20:00Z',
      completed: true,
      active: false,
      icon: Package
    },
    {
      id: 'shipped',
      title: 'تم الشحن',
      description: 'تم شحن طلبك وهو في الطريق إليك',
      timestamp: '2024-01-16T09:15:00Z',
      completed: true,
      active: true,
      icon: Truck
    },
    {
      id: 'out_for_delivery',
      title: 'خرج للتسليم',
      description: 'طلبك مع مندوب التوصيل وسيصل قريباً',
      timestamp: null,
      completed: false,
      active: false,
      icon: MapPin
    },
    {
      id: 'delivered',
      title: 'تم التسليم',
      description: 'تم تسليم طلبك بنجاح',
      timestamp: null,
      completed: false,
      active: false,
      icon: CheckCircle
    }
  ]
}

const OrderTrackingPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>()

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'طلباتي', href: '/orders' },
    { label: 'تتبع الطلب', current: true }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ordered':
        return <Badge variant="info" size="sm">مؤكد</Badge>
      case 'processing':
        return <Badge variant="warning" size="sm">قيد التحضير</Badge>
      case 'shipped':
        return <Badge variant="primary" size="sm">تم الشحن</Badge>
      case 'out_for_delivery':
        return <Badge variant="warning" size="sm">خرج للتسليم</Badge>
      case 'delivered':
        return <Badge variant="success" size="sm">تم التسليم</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <OrderHeader padding="lg">
        <HeaderContent>
          <OrderInfo>
            <OrderNumber>
              <Package size={24} />
              طلب رقم {mockTracking.orderId}
            </OrderNumber>
            <OrderMeta>
              <div>تاريخ الطلب: {formatDate(mockTracking.orderDate)}</div>
              <div>التسليم المتوقع: {formatDate(mockTracking.estimatedDelivery)}</div>
              <div>رقم التتبع: {mockTracking.trackingNumber}</div>
            </OrderMeta>
          </OrderInfo>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', alignItems: 'flex-end' }}>
            {getStatusBadge(mockTracking.currentStatus)}
          </div>
        </HeaderContent>
      </OrderHeader>

      <TrackingProgress padding="lg">
        <ProgressTitle>حالة الطلب</ProgressTitle>
        <ProgressSteps>
          {mockTracking.steps.map((step) => (
            <ProgressStep key={step.id} $completed={step.completed} $active={step.active}>
              <StepIcon $completed={step.completed} $active={step.active}>
                <step.icon size={20} />
              </StepIcon>
              <StepContent>
                <StepTitle $completed={step.completed} $active={step.active}>
                  {step.title}
                </StepTitle>
                <StepDescription>{step.description}</StepDescription>
                {step.timestamp && (
                  <StepTime>
                    {formatDate(step.timestamp)} في {formatTime(step.timestamp)}
                  </StepTime>
                )}
              </StepContent>
            </ProgressStep>
          ))}
        </ProgressSteps>
      </TrackingProgress>

      <DeliveryInfo padding="lg">
        <ProgressTitle>معلومات التسليم</ProgressTitle>
        <InfoGrid>
          <InfoSection>
            <InfoLabel>عنوان التسليم</InfoLabel>
            <InfoValue>
              {mockTracking.deliveryAddress.name}<br />
              {mockTracking.deliveryAddress.address}<br />
              {mockTracking.deliveryAddress.city}, {mockTracking.deliveryAddress.postalCode}<br />
              {mockTracking.deliveryAddress.phone}
            </InfoValue>
          </InfoSection>

          <InfoSection>
            <InfoLabel>شركة الشحن</InfoLabel>
            <InfoValue>
              {mockTracking.carrier}
              <ContactInfo>
                <ContactButton variant="outline" size="sm">
                  <Phone size={14} />
                  {mockTracking.carrierPhone}
                </ContactButton>
                <ContactButton variant="outline" size="sm">
                  <Mail size={14} />
                  تواصل
                </ContactButton>
              </ContactInfo>
            </InfoValue>
          </InfoSection>
        </InfoGrid>
      </DeliveryInfo>

      <div style={{ display: 'flex', justifyContent: 'center', gap: '1rem' }}>
        <Button variant="primary" onClick={() => window.location.href = '/orders'}>
          عرض جميع طلباتي
        </Button>
        <Button variant="outline" onClick={() => window.location.href = '/products'}>
          متابعة التسوق
        </Button>
      </div>
    </Container>
  )
}

export default OrderTrackingPage
