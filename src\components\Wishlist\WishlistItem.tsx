import React from 'react'
import styled from 'styled-components'
import { Heart, ShoppingCart, Trash2 } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useWishlist } from '../../context/WishlistContext'
import { useCart } from '../../context/CartContext'
import { formatPrice } from '../../utils/formatters'
import Button from '../UI/Button'
import Card from '../UI/Card'
import Badge from '../UI/Badge'
import type { Product } from '../../types'

interface WishlistItemProps {
  product: Product
}

const ItemCard = styled(Card)`
  position: relative;
  transition: all ${({ theme }) => theme.transitions.normal};
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
`

const ProductImage = styled.img`
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  cursor: pointer;
  transition: transform ${({ theme }) => theme.transitions.normal};
  
  &:hover {
    transform: scale(1.02);
  }
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`

const ProductName = styled.h3`
  font-size: 1rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: 1.4;
  cursor: pointer;
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`

const ProductCategory = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const PriceSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  margin: ${({ theme }) => theme.spacing.sm} 0;
`

const CurrentPrice = styled.span`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.primary};
`

const OriginalPrice = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.light};
  text-decoration: line-through;
`

const DiscountBadge = styled(Badge)`
  margin-right: auto;
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-top: ${({ theme }) => theme.spacing.md};
`

const RemoveButton = styled.button`
  position: absolute;
  top: ${({ theme }) => theme.spacing.sm};
  left: ${({ theme }) => theme.spacing.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background-color: rgba(255, 255, 255, 0.9);
  color: ${({ theme }) => theme.colors.status.error};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.status.error};
    color: white;
    transform: scale(1.1);
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.status.error};
    outline-offset: 2px;
  }
`

const StockStatus = styled.div<{ $inStock: boolean }>`
  font-size: 0.75rem;
  color: ${({ $inStock, theme }) => 
    $inStock ? theme.colors.status.success : theme.colors.status.error};
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
`

const WishlistItem: React.FC<WishlistItemProps> = ({ product }) => {
  const { removeFromWishlist } = useWishlist()
  const { addToCart } = useCart()
  const navigate = useNavigate()

  const handleRemoveFromWishlist = () => {
    removeFromWishlist(product.id)
  }

  const handleAddToCart = () => {
    if (product.stock && product.stock > 0) {
      addToCart({
        id: product.id,
        name: product.name,
        price: product.salePrice || product.price,
        image: product.images[0],
        quantity: 1,
        stock: product.stock
      })
    }
  }

  const handleViewProduct = () => {
    navigate(`/products/${product.id}`)
  }

  const isOnSale = product.salePrice && product.salePrice < product.price
  const discountPercentage = isOnSale 
    ? Math.round(((product.price - product.salePrice!) / product.price) * 100)
    : 0

  const isInStock = product.stock && product.stock > 0

  return (
    <ItemCard padding="md">
      <RemoveButton
        onClick={handleRemoveFromWishlist}
        aria-label={`إزالة ${product.name} من المفضلة`}
      >
        <Trash2 size={18} />
      </RemoveButton>

      <ProductImage
        src={product.images[0]}
        alt={product.name}
        onClick={handleViewProduct}
        loading="lazy"
      />

      <ProductInfo>
        <ProductCategory>{product.category}</ProductCategory>
        
        <ProductName onClick={handleViewProduct}>
          {product.name}
        </ProductName>

        <PriceSection>
          <CurrentPrice>
            {formatPrice(product.salePrice || product.price)}
          </CurrentPrice>
          
          {isOnSale && (
            <>
              <OriginalPrice>
                {formatPrice(product.price)}
              </OriginalPrice>
              <DiscountBadge variant="error" size="sm">
                -{discountPercentage}%
              </DiscountBadge>
            </>
          )}
        </PriceSection>

        <StockStatus $inStock={!!isInStock}>
          {isInStock ? `متوفر (${product.stock})` : 'غير متوفر'}
        </StockStatus>

        <ActionButtons>
          <Button
            variant="primary"
            size="sm"
            onClick={handleAddToCart}
            disabled={!isInStock}
            fullWidth
          >
            <ShoppingCart size={16} />
            {isInStock ? 'أضف للسلة' : 'غير متوفر'}
          </Button>
        </ActionButtons>
      </ProductInfo>
    </ItemCard>
  )
}

export default WishlistItem
