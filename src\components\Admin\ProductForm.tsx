import React, { useState } from 'react'
import styled from 'styled-components'
import { Save, X, Plus, Trash2, Upload } from 'lucide-react'
import Button from '../UI/Button'
import Input from '../UI/Input'
import Textarea from '../UI/Textarea'
import Select from '../UI/Select'
import Card from '../UI/Card'
import Alert from '../UI/Alert'
import type { Product } from '../../types'

interface ProductFormProps {
  product?: Product
  onSave: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void
  onCancel: () => void
  isOpen: boolean
}

const FormContainer = styled(Card)`
  max-width: 800px;
  margin: 0 auto;
`

const FormHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const FormTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`

const FormField = styled.div<{ $fullWidth?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  
  ${({ $fullWidth }) => $fullWidth && `
    grid-column: 1 / -1;
  `}
`

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const ImageUploadSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const ImageGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`

const ImagePreview = styled.div`
  position: relative;
  aspect-ratio: 1;
  border: 2px dashed ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
  background-color: ${({ theme }) => theme.colors.surface};
`

const PreviewImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`

const UploadPlaceholder = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${({ theme }) => theme.colors.text.light};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.border.light};
  }
`

const RemoveImageButton = styled.button`
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background-color: ${({ theme }) => theme.colors.status.error};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity ${({ theme }) => theme.transitions.fast};
  
  ${ImagePreview}:hover & {
    opacity: 1;
  }
`

const VariantSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const VariantHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const VariantTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const VariantList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const VariantTag = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: 0.875rem;
`

const RemoveVariantButton = styled.button`
  border: none;
  background: none;
  color: ${({ theme }) => theme.colors.status.error};
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  
  &:hover {
    color: ${({ theme }) => theme.colors.status.error};
  }
`

const AddVariantForm = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  align-items: end;
`

const FormActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  justify-content: flex-end;
  padding-top: ${({ theme }) => theme.spacing.lg};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`

const ProductForm: React.FC<ProductFormProps> = ({ product, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price || 0,
    salePrice: product?.salePrice || 0,
    category: product?.category || '',
    stock: product?.stock || 0,
    images: product?.images || [],
    sizes: product?.sizes || [],
    colors: product?.colors || []
  })

  const [newSize, setNewSize] = useState('')
  const [newColor, setNewColor] = useState('')
  const [errors, setErrors] = useState<string[]>([])

  const categoryOptions = [
    { value: 'قمصان', label: 'قمصان' },
    { value: 'بناطيل', label: 'بناطيل' },
    { value: 'فساتين', label: 'فساتين' },
    { value: 'أحذية', label: 'أحذية' },
    { value: 'إكسسوارات', label: 'إكسسوارات' }
  ]

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      // In a real app, you would upload these to a server
      // For now, we'll create object URLs for preview
      const newImages = Array.from(files).map(file => URL.createObjectURL(file))
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...newImages]
      }))
    }
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const addSize = () => {
    if (newSize.trim() && !formData.sizes.includes(newSize.trim())) {
      setFormData(prev => ({
        ...prev,
        sizes: [...prev.sizes, newSize.trim()]
      }))
      setNewSize('')
    }
  }

  const removeSize = (size: string) => {
    setFormData(prev => ({
      ...prev,
      sizes: prev.sizes.filter(s => s !== size)
    }))
  }

  const addColor = () => {
    if (newColor.trim() && !formData.colors.includes(newColor.trim())) {
      setFormData(prev => ({
        ...prev,
        colors: [...prev.colors, newColor.trim()]
      }))
      setNewColor('')
    }
  }

  const removeColor = (color: string) => {
    setFormData(prev => ({
      ...prev,
      colors: prev.colors.filter(c => c !== color)
    }))
  }

  const validateForm = () => {
    const newErrors: string[] = []
    
    if (!formData.name.trim()) newErrors.push('اسم المنتج مطلوب')
    if (!formData.description.trim()) newErrors.push('وصف المنتج مطلوب')
    if (formData.price <= 0) newErrors.push('السعر يجب أن يكون أكبر من صفر')
    if (!formData.category) newErrors.push('فئة المنتج مطلوبة')
    if (formData.stock < 0) newErrors.push('الكمية لا يمكن أن تكون سالبة')
    if (formData.images.length === 0) newErrors.push('صورة واحدة على الأقل مطلوبة')
    
    setErrors(newErrors)
    return newErrors.length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onSave({
        ...formData,
        id: product?.id || `product-${Date.now()}`,
        createdAt: product?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })
    }
  }

  return (
    <FormContainer padding="lg">
      <FormHeader>
        <FormTitle>
          {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </FormTitle>
        <Button variant="outline" size="sm" onClick={onCancel}>
          <X size={16} />
          إلغاء
        </Button>
      </FormHeader>

      {errors.length > 0 && (
        <Alert variant="error" style={{ marginBottom: '1rem' }}>
          <ul style={{ margin: 0, paddingRight: '1rem' }}>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <FormGrid>
          <FormField $fullWidth>
            <Label>اسم المنتج *</Label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="أدخل اسم المنتج"
              required
            />
          </FormField>

          <FormField $fullWidth>
            <Label>وصف المنتج *</Label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="أدخل وصف المنتج"
              rows={4}
              required
            />
          </FormField>

          <FormField>
            <Label>السعر الأساسي *</Label>
            <Input
              type="number"
              value={formData.price}
              onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
              placeholder="0.00"
              min="0"
              step="0.01"
              required
            />
          </FormField>

          <FormField>
            <Label>سعر التخفيض</Label>
            <Input
              type="number"
              value={formData.salePrice}
              onChange={(e) => handleInputChange('salePrice', parseFloat(e.target.value) || 0)}
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </FormField>

          <FormField>
            <Label>الفئة *</Label>
            <Select
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              options={categoryOptions}
              placeholder="اختر الفئة"
              required
            />
          </FormField>

          <FormField>
            <Label>الكمية المتوفرة *</Label>
            <Input
              type="number"
              value={formData.stock}
              onChange={(e) => handleInputChange('stock', parseInt(e.target.value) || 0)}
              placeholder="0"
              min="0"
              required
            />
          </FormField>
        </FormGrid>

        <ImageUploadSection>
          <Label>صور المنتج *</Label>
          <ImageGrid>
            {formData.images.map((image, index) => (
              <ImagePreview key={index}>
                <PreviewImage src={image} alt={`Product ${index + 1}`} />
                <RemoveImageButton onClick={() => removeImage(index)}>
                  <X size={12} />
                </RemoveImageButton>
              </ImagePreview>
            ))}
            <ImagePreview>
              <UploadPlaceholder onClick={() => document.getElementById('image-upload')?.click()}>
                <Upload size={24} />
                <span style={{ fontSize: '0.75rem', marginTop: '4px' }}>إضافة صورة</span>
              </UploadPlaceholder>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                multiple
                style={{ display: 'none' }}
                onChange={handleImageUpload}
              />
            </ImagePreview>
          </ImageGrid>
        </ImageUploadSection>

        <VariantSection>
          <VariantHeader>
            <VariantTitle>المقاسات المتوفرة</VariantTitle>
          </VariantHeader>
          <VariantList>
            {formData.sizes.map((size) => (
              <VariantTag key={size}>
                {size}
                <RemoveVariantButton onClick={() => removeSize(size)}>
                  <X size={14} />
                </RemoveVariantButton>
              </VariantTag>
            ))}
          </VariantList>
          <AddVariantForm>
            <Input
              type="text"
              value={newSize}
              onChange={(e) => setNewSize(e.target.value)}
              placeholder="مقاس جديد (مثل: S, M, L)"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSize())}
            />
            <Button type="button" variant="outline" size="sm" onClick={addSize}>
              <Plus size={16} />
              إضافة
            </Button>
          </AddVariantForm>
        </VariantSection>

        <VariantSection>
          <VariantHeader>
            <VariantTitle>الألوان المتوفرة</VariantTitle>
          </VariantHeader>
          <VariantList>
            {formData.colors.map((color) => (
              <VariantTag key={color}>
                {color}
                <RemoveVariantButton onClick={() => removeColor(color)}>
                  <X size={14} />
                </RemoveVariantButton>
              </VariantTag>
            ))}
          </VariantList>
          <AddVariantForm>
            <Input
              type="text"
              value={newColor}
              onChange={(e) => setNewColor(e.target.value)}
              placeholder="لون جديد (مثل: أحمر, أزرق)"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addColor())}
            />
            <Button type="button" variant="outline" size="sm" onClick={addColor}>
              <Plus size={16} />
              إضافة
            </Button>
          </AddVariantForm>
        </VariantSection>

        <FormActions>
          <Button type="submit" variant="primary" size="lg">
            <Save size={20} />
            {product ? 'حفظ التغييرات' : 'إضافة المنتج'}
          </Button>
          <Button type="button" variant="outline" size="lg" onClick={onCancel}>
            إلغاء
          </Button>
        </FormActions>
      </form>
    </FormContainer>
  )
}

export default ProductForm
