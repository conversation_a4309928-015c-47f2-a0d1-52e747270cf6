import React, { useState } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import styled, { keyframes } from 'styled-components'
import { useAuth } from '../../context/AuthContext'
import { Mail, Lock, Eye, EyeOff, ShoppingBag } from 'lucide-react'
import type { LoginForm as LoginFormData } from '../../types'
import Button from '../UI/Button'
import Input from '../UI/Input'
import LoadingSpinner from '../UI/LoadingSpinner'

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`

const FormContainer = styled.div`
  max-width: 450px;
  width: 100%;
  background: ${({ theme }) => theme.colors.background};
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid ${({ theme }) => theme.colors.border};
  overflow: hidden;
  animation: ${fadeIn} 0.6s ease-out;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #000 0%, #333 50%, #000 100%);
  }
`

const LoginHeader = styled.div`
  padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.lg};
  text-align: center;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`

const BrandIcon = styled.div`
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #000 0%, #333 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing.md};
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);

  svg {
    color: white;
  }
`

const Title = styled.h1`
  font-size: 1.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.text.primary};
  font-family: 'Cairo', sans-serif;
`

const Subtitle = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.5;
`

const FormBody = styled.div`
  padding: ${({ theme }) => theme.spacing.xl};
`

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`

const InputGroup = styled.div`
  position: relative;
`

const InputIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.secondary};
  z-index: 2;

  svg {
    width: 18px;
    height: 18px;
  }
`

const PasswordToggle = styled.button`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: ${({ theme }) => theme.colors.text.primary};
    background: ${({ theme }) => theme.colors.surface};
  }

  svg {
    width: 18px;
    height: 18px;
  }
`

const StyledInput = styled(Input)`
  padding-right: 45px;
  padding-left: 45px;
  height: 50px;
  border-radius: 12px;
  border: 2px solid ${({ theme }) => theme.colors.border};
  transition: all 0.3s ease;
  font-size: 0.95rem;

  &:focus {
    border-color: ${({ theme }) => theme.colors.text.primary};
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
  }
`

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colors.status.error};
  font-size: 0.875rem;
  text-align: center;
  padding: ${({ theme }) => theme.spacing.md};
  background: ${({ theme }) => theme.colors.status.error}08;
  border-radius: 12px;
  border: 1px solid ${({ theme }) => theme.colors.status.error}20;
  margin-top: -8px;
`

const LoginButton = styled(Button)`
  height: 50px;
  border-radius: 12px;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  font-size: 1rem;
  background: linear-gradient(135deg, #000 0%, #333 100%);
  border: none;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #333 0%, #000 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
`

const Divider = styled.div`
  display: flex;
  align-items: center;
  margin: ${({ theme }) => theme.spacing.lg} 0;

  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: ${({ theme }) => theme.colors.border};
  }

  span {
    padding: 0 ${({ theme }) => theme.spacing.md};
    color: ${({ theme }) => theme.colors.text.secondary};
    font-size: 0.875rem;
  }
`

const GuestButton = styled(Button)`
  height: 50px;
  border-radius: 12px;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  border: 2px solid ${({ theme }) => theme.colors.border};
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    border-color: ${({ theme }) => theme.colors.text.primary};
    background: ${({ theme }) => theme.colors.text.primary};
    color: white;
    transform: translateY(-1px);
  }
`

const AdminHint = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid ${({ theme }) => theme.colors.border};
  text-align: center;

  h4 {
    margin: 0 0 ${({ theme }) => theme.spacing.sm};
    color: ${({ theme }) => theme.colors.text.primary};
    font-size: 0.9rem;
    font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  }

  p {
    margin: 0;
    font-size: 0.8rem;
    color: ${({ theme }) => theme.colors.text.secondary};
    line-height: 1.4;
  }

  code {
    background: ${({ theme }) => theme.colors.background};
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    color: ${({ theme }) => theme.colors.text.primary};
  }
`

const LinkContainer = styled.div`
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing.lg};
  padding-top: ${({ theme }) => theme.spacing.lg};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`

const StyledLink = styled(Link)`
  color: ${({ theme }) => theme.colors.text.primary};
  text-decoration: none;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};

  &:hover {
    text-decoration: underline;
  }
`

const LoginForm: React.FC = () => {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  })
  const [error, setError] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const { login, loginAsGuest } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const from = (location.state as any)?.from?.pathname || '/'

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (error) setError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      await login(formData)
      navigate(from, { replace: true })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع')
    } finally {
      setIsLoading(false)
    }
  }

  const handleGuestLogin = () => {
    loginAsGuest()
    navigate(from, { replace: true })
  }

  return (
    <FormContainer>
      <LoginHeader>
        <BrandIcon>
          <ShoppingBag size={28} />
        </BrandIcon>
        <Title>مرحباً بك</Title>
        <Subtitle>سجل دخولك للوصول إلى حسابك</Subtitle>
      </LoginHeader>

      <FormBody>
        <Form onSubmit={handleSubmit}>
          <InputGroup>
            <InputIcon>
              <Mail />
            </InputIcon>
            <StyledInput
              type="email"
              name="email"
              placeholder="البريد الإلكتروني"
              value={formData.email}
              onChange={handleChange}
              required
              disabled={isLoading}
            />
          </InputGroup>

          <InputGroup>
            <InputIcon>
              <Lock />
            </InputIcon>
            <StyledInput
              type={showPassword ? "text" : "password"}
              name="password"
              placeholder="كلمة المرور"
              value={formData.password}
              onChange={handleChange}
              required
              disabled={isLoading}
            />
            <PasswordToggle
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? <EyeOff /> : <Eye />}
            </PasswordToggle>
          </InputGroup>

          {error && <ErrorMessage>{error}</ErrorMessage>}

          <LoginButton
            type="submit"
            disabled={isLoading}
            fullWidth
          >
            {isLoading ? <LoadingSpinner /> : 'تسجيل الدخول'}
          </LoginButton>

          <Divider>
            <span>أو</span>
          </Divider>

          <GuestButton
            type="button"
            variant="outline"
            onClick={handleGuestLogin}
            disabled={isLoading}
            fullWidth
          >
            متابعة كضيف
          </GuestButton>
        </Form>

        <AdminHint>
          <h4>💡 للوصول إلى لوحة الإدارة</h4>
          <p>
            استخدم أي بريد إلكتروني يحتوي على <code>admin</code><br />
            مثال: <code><EMAIL></code>
          </p>
        </AdminHint>

        <LinkContainer>
          <p>ليس لديك حساب؟ <StyledLink to="/register">إنشاء حساب جديد</StyledLink></p>
        </LinkContainer>
      </FormBody>
    </FormContainer>
  )
}

export default LoginForm
