/* RTL Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  direction: rtl;
  text-align: right;
  scroll-behavior: smooth;
}

body {
  font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', '<PERSON>xygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #ffffff;
  color: #000000;
  line-height: 1.6;
  font-size: 16px;
}

/* RTL-specific adjustments */
input, textarea, select {
  direction: rtl;
  text-align: right;
}

/* Arabic number formatting */
.arabic-numbers {
  font-feature-settings: "lnum" 1;
}

/* Utility classes for RTL */
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-center { text-align: center; }

.float-left { float: left; }
.float-right { float: right; }

.mr-auto { margin-right: auto; }
.ml-auto { margin-left: auto; }

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading {
  animation: spin 1s linear infinite;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #000000;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
}
