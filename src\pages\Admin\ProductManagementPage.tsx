import React, { useState } from 'react'
import styled from 'styled-components'
import { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react'
import { useAdmin } from '../../context/AdminContext'
import { formatPrice } from '../../utils/formatters'
import Breadcrumb from '../../components/UI/Breadcrumb'
import Button from '../../components/UI/Button'
import Input from '../../components/UI/Input'
import Select from '../../components/UI/Select'
import Card from '../../components/UI/Card'
import Badge from '../../components/UI/Badge'
import Modal from '../../components/UI/Modal'
import Pagination from '../../components/UI/Pagination'
import ProductForm from '../../components/Admin/ProductForm'
import type { Product } from '../../types'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const ProductsCount = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const FiltersSection = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: column;
  }
`

const SearchWrapper = styled.div`
  flex: 1;
  position: relative;
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.light};
  width: 18px;
  height: 18px;
  pointer-events: none;
`

const FilterControls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`

const ProductsTable = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  overflow: hidden;
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px 120px 100px 120px;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
`

const ProductRow = styled.div`
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px 120px 100px 120px;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  align-items: center;
  transition: background-color ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block;
    padding: ${({ theme }) => theme.spacing.lg};
    border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  }
`

const ProductImage = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    margin-bottom: ${({ theme }) => theme.spacing.md};
  }
`

const ProductName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ProductCategory = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const PriceCell = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const CurrentPrice = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const OriginalPrice = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.light};
  text-decoration: line-through;
`

const StockCell = styled.div<{ $lowStock: boolean }>`
  color: ${({ $lowStock, theme }) => 
    $lowStock ? theme.colors.status.warning : theme.colors.text.primary};
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
`

const ActionsCell = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    margin-top: ${({ theme }) => theme.spacing.md};
  }
`

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  &.edit:hover {
    border-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.primary};
  }
  
  &.delete:hover {
    border-color: ${({ theme }) => theme.colors.status.error};
    color: ${({ theme }) => theme.colors.status.error};
  }
`

const MobileProductCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  @media (min-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
`

const ITEMS_PER_PAGE = 10

const ProductManagementPage: React.FC = () => {
  const { products, addProduct, updateProduct, deleteProduct } = useAdmin()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [currentPage, setCurrentPage] = useState(1)
  const [showProductForm, setShowProductForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | undefined>()

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'لوحة التحكم', href: '/admin' },
    { label: 'إدارة المنتجات', current: true }
  ]

  // Get unique categories
  const categories = [...new Set(products.map(p => p.category))]
    .map(cat => ({ value: cat, label: cat }))

  // Filter and sort products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !selectedCategory || product.category === selectedCategory
    
    return matchesSearch && matchesCategory
  }).sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return (a.salePrice || a.price) - (b.salePrice || b.price)
      case 'price-high':
        return (b.salePrice || b.price) - (a.salePrice || a.price)
      case 'stock-low':
        return (a.stock || 0) - (b.stock || 0)
      case 'stock-high':
        return (b.stock || 0) - (a.stock || 0)
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      default:
        return a.name.localeCompare(b.name, 'ar')
    }
  })

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + ITEMS_PER_PAGE)

  const sortOptions = [
    { value: 'name', label: 'الاسم (أ-ي)' },
    { value: 'price-low', label: 'السعر (من الأقل للأعلى)' },
    { value: 'price-high', label: 'السعر (من الأعلى للأقل)' },
    { value: 'stock-low', label: 'المخزون (من الأقل للأعلى)' },
    { value: 'stock-high', label: 'المخزون (من الأعلى للأقل)' },
    { value: 'newest', label: 'الأحدث' }
  ]

  const handleAddProduct = () => {
    setEditingProduct(undefined)
    setShowProductForm(true)
  }

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product)
    setShowProductForm(true)
  }

  const handleDeleteProduct = async (productId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      try {
        await deleteProduct(productId)
        alert('تم حذف المنتج بنجاح')
      } catch (error) {
        alert('حدث خطأ أثناء حذف المنتج')
      }
    }
  }

  const handleSaveProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      if (editingProduct) {
        await updateProduct(editingProduct.id, productData)
        alert('تم تحديث المنتج بنجاح')
      } else {
        await addProduct(productData)
        alert('تم إضافة المنتج بنجاح')
      }
      setShowProductForm(false)
      setEditingProduct(undefined)
    } catch (error) {
      alert('حدث خطأ أثناء حفظ المنتج')
    }
  }

  const getStockStatus = (stock: number) => {
    if (stock === 0) return <Badge variant="error" size="sm">نفد المخزون</Badge>
    if (stock <= 5) return <Badge variant="warning" size="sm">مخزون منخفض</Badge>
    return <Badge variant="success" size="sm">متوفر</Badge>
  }

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <PageHeader>
        <div>
          <Title>إدارة المنتجات</Title>
          <ProductsCount>
            {filteredProducts.length} {filteredProducts.length === 1 ? 'منتج' : 'منتجات'}
          </ProductsCount>
        </div>
        
        <Button variant="primary" onClick={handleAddProduct}>
          <Plus size={20} />
          إضافة منتج جديد
        </Button>
      </PageHeader>

      <FiltersSection>
        <SearchWrapper>
          <Input
            type="text"
            placeholder="البحث في المنتجات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />
          <SearchIcon />
        </SearchWrapper>
        
        <FilterControls>
          <Select
            placeholder="جميع الفئات"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            options={[
              { value: '', label: 'جميع الفئات' },
              ...categories
            ]}
          />
          
          <Select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            options={sortOptions}
          />
        </FilterControls>
      </FiltersSection>

      <ProductsTable>
        <TableHeader>
          <div>الصورة</div>
          <div>المنتج</div>
          <div>السعر</div>
          <div>سعر التخفيض</div>
          <div>المخزون</div>
          <div>الحالة</div>
          <div>الإجراءات</div>
        </TableHeader>

        {paginatedProducts.map((product) => (
          <React.Fragment key={product.id}>
            {/* Desktop View */}
            <ProductRow>
              <ProductImage src={product.images[0]} alt={product.name} />
              <ProductInfo>
                <ProductName>{product.name}</ProductName>
                <ProductCategory>{product.category}</ProductCategory>
              </ProductInfo>
              <CurrentPrice>{formatPrice(product.price)}</CurrentPrice>
              <div>
                {product.salePrice ? formatPrice(product.salePrice) : '-'}
              </div>
              <StockCell $lowStock={(product.stock || 0) <= 5}>
                {product.stock || 0}
              </StockCell>
              <div>{getStockStatus(product.stock || 0)}</div>
              <ActionsCell>
                <ActionButton
                  className="edit"
                  onClick={() => handleEditProduct(product)}
                  title="تعديل"
                >
                  <Edit size={16} />
                </ActionButton>
                <ActionButton
                  onClick={() => window.open(`/products/${product.id}`, '_blank')}
                  title="عرض"
                >
                  <Eye size={16} />
                </ActionButton>
                <ActionButton
                  className="delete"
                  onClick={() => handleDeleteProduct(product.id)}
                  title="حذف"
                >
                  <Trash2 size={16} />
                </ActionButton>
              </ActionsCell>
            </ProductRow>

            {/* Mobile View */}
            <MobileProductCard padding="md">
              <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
                <ProductImage src={product.images[0]} alt={product.name} />
                <ProductInfo>
                  <ProductName>{product.name}</ProductName>
                  <ProductCategory>{product.category}</ProductCategory>
                  <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center', marginTop: '0.5rem' }}>
                    <CurrentPrice>{formatPrice(product.price)}</CurrentPrice>
                    {product.salePrice && (
                      <OriginalPrice>{formatPrice(product.salePrice)}</OriginalPrice>
                    )}
                  </div>
                </ProductInfo>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <div>
                  <span style={{ fontSize: '0.875rem', color: '#666' }}>المخزون: </span>
                  <StockCell $lowStock={(product.stock || 0) <= 5}>
                    {product.stock || 0}
                  </StockCell>
                </div>
                {getStockStatus(product.stock || 0)}
              </div>
              
              <ActionsCell>
                <Button variant="outline" size="sm" onClick={() => handleEditProduct(product)}>
                  <Edit size={16} />
                  تعديل
                </Button>
                <Button variant="outline" size="sm" onClick={() => window.open(`/products/${product.id}`, '_blank')}>
                  <Eye size={16} />
                  عرض
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleDeleteProduct(product.id)}>
                  <Trash2 size={16} />
                  حذف
                </Button>
              </ActionsCell>
            </MobileProductCard>
          </React.Fragment>
        ))}
      </ProductsTable>

      {totalPages > 1 && (
        <div style={{ marginTop: '2rem' }}>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}

      <ProductForm
        product={editingProduct}
        onSave={handleSaveProduct}
        onCancel={() => setShowProductForm(false)}
        isOpen={showProductForm}
      />
    </Container>
  )
}

export default ProductManagementPage
