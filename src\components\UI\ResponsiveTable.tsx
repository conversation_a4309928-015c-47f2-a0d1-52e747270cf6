import React from 'react'
import styled from 'styled-components'
import Card from './Card'

interface Column {
  key: string
  label: string
  width?: string
  hideOnMobile?: boolean
  render?: (value: any, row: any) => React.ReactNode
}

interface ResponsiveTableProps {
  columns: Column[]
  data: any[]
  keyField?: string
  title?: string
  actions?: React.ReactNode
  emptyMessage?: string
  mobileCardRender?: (row: any, index: number) => React.ReactNode
}

const TableContainer = styled(Card)`
  overflow: hidden;
`

const TableHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const TableTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const DesktopTable = styled.div`
  overflow-x: auto;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
`

const TableGrid = styled.div<{ $columns: number }>`
  display: grid;
  grid-template-columns: ${({ $columns }) => `repeat(${$columns}, minmax(120px, 1fr))`};
  gap: ${({ theme }) => theme.spacing.md};
  min-width: 800px;
`

const TableHeaderRow = styled.div`
  display: contents;
  
  > div {
    font-weight: ${({ theme }) => theme.fonts.weights.semibold};
    color: ${({ theme }) => theme.colors.text.primary};
    padding: ${({ theme }) => theme.spacing.md};
    background-color: ${({ theme }) => theme.colors.surface};
    border-bottom: 2px solid ${({ theme }) => theme.colors.border.medium};
    
    &:first-child {
      border-top-right-radius: ${({ theme }) => theme.borderRadius.md};
    }
    
    &:last-child {
      border-top-left-radius: ${({ theme }) => theme.borderRadius.md};
    }
  }
`

const TableRow = styled.div`
  display: contents;
  
  &:hover > div {
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  > div {
    padding: ${({ theme }) => theme.spacing.md};
    border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
    display: flex;
    align-items: center;
    transition: background-color ${({ theme }) => theme.transitions.fast};
  }
`

const MobileCards = styled.div`
  display: none;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block;
  }
`

const MobileCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  &:last-child {
    margin-bottom: 0;
  }
`

const MobileCardContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`

const MobileField = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xs} 0;
  
  &:not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  }
`

const MobileFieldLabel = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const MobileFieldValue = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  text-align: left;
`

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1rem;
`

const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  columns,
  data,
  keyField = 'id',
  title,
  actions,
  emptyMessage = 'لا توجد بيانات للعرض',
  mobileCardRender
}) => {
  const visibleColumns = columns.filter(col => !col.hideOnMobile)
  const desktopColumns = columns

  const renderCellValue = (column: Column, row: any) => {
    if (column.render) {
      return column.render(row[column.key], row)
    }
    return row[column.key]
  }

  const renderMobileCard = (row: any, index: number) => {
    if (mobileCardRender) {
      return mobileCardRender(row, index)
    }

    return (
      <MobileCard key={row[keyField] || index} padding="md">
        <MobileCardContent>
          {visibleColumns.map((column) => (
            <MobileField key={column.key}>
              <MobileFieldLabel>{column.label}</MobileFieldLabel>
              <MobileFieldValue>
                {renderCellValue(column, row)}
              </MobileFieldValue>
            </MobileField>
          ))}
        </MobileCardContent>
      </MobileCard>
    )
  }

  return (
    <TableContainer padding="lg">
      {(title || actions) && (
        <TableHeader>
          {title && <TableTitle>{title}</TableTitle>}
          {actions && <div>{actions}</div>}
        </TableHeader>
      )}

      {data.length === 0 ? (
        <EmptyState>{emptyMessage}</EmptyState>
      ) : (
        <>
          <DesktopTable>
            <TableGrid $columns={desktopColumns.length}>
              <TableHeaderRow>
                {desktopColumns.map((column) => (
                  <div key={column.key}>{column.label}</div>
                ))}
              </TableHeaderRow>
              
              {data.map((row, index) => (
                <TableRow key={row[keyField] || index}>
                  {desktopColumns.map((column) => (
                    <div key={column.key}>
                      {renderCellValue(column, row)}
                    </div>
                  ))}
                </TableRow>
              ))}
            </TableGrid>
          </DesktopTable>

          <MobileCards>
            {data.map((row, index) => renderMobileCard(row, index))}
          </MobileCards>
        </>
      )}
    </TableContainer>
  )
}

export default ResponsiveTable
