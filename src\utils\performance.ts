// Performance optimization utilities

// Debounce function for search inputs and API calls
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

// Throttle function for scroll events and resize handlers
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

// Lazy loading utility for images
export const createIntersectionObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options?: IntersectionObserverInit
): IntersectionObserver => {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  }
  
  return new IntersectionObserver(callback, defaultOptions)
}

// Memoization utility for expensive calculations
export const memoize = <T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T => {
  const cache = new Map<string, ReturnType<T>>()
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = getKey ? getKey(...args) : JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)!
    }
    
    const result = func(...args)
    cache.set(key, result)
    return result
  }) as T
}

// Local storage with error handling and expiration
export const storage = {
  set: (key: string, value: any, expirationHours?: number): void => {
    try {
      const item = {
        value,
        timestamp: Date.now(),
        expiration: expirationHours ? Date.now() + (expirationHours * 60 * 60 * 1000) : null
      }
      localStorage.setItem(key, JSON.stringify(item))
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  },
  
  get: <T = any>(key: string): T | null => {
    try {
      const item = localStorage.getItem(key)
      if (!item) return null
      
      const parsed = JSON.parse(item)
      
      // Check expiration
      if (parsed.expiration && Date.now() > parsed.expiration) {
        localStorage.removeItem(key)
        return null
      }
      
      return parsed.value
    } catch (error) {
      console.warn('Failed to read from localStorage:', error)
      return null
    }
  },
  
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error)
    }
  },
  
  clear: (): void => {
    try {
      localStorage.clear()
    } catch (error) {
      console.warn('Failed to clear localStorage:', error)
    }
  }
}

// Image optimization utilities
export const getOptimizedImageUrl = (
  url: string,
  width?: number,
  height?: number,
  quality: number = 80
): string => {
  // In a real application, you might use a service like Cloudinary or ImageKit
  // For now, we'll return the original URL
  return url
}

// Preload critical resources
export const preloadResource = (href: string, as: string): void => {
  const link = document.createElement('link')
  link.rel = 'preload'
  link.href = href
  link.as = as
  document.head.appendChild(link)
}

// Prefetch resources for better navigation
export const prefetchResource = (href: string): void => {
  const link = document.createElement('link')
  link.rel = 'prefetch'
  link.href = href
  document.head.appendChild(link)
}

// Critical CSS inlining utility
export const inlineCriticalCSS = (css: string): void => {
  const style = document.createElement('style')
  style.textContent = css
  document.head.appendChild(style)
}

// Bundle size analysis helper
export const logBundleSize = (): void => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Bundle analysis available at build time')
  }
}

// Performance monitoring
export const measurePerformance = (name: string, fn: () => void): void => {
  if (process.env.NODE_ENV === 'development') {
    const start = performance.now()
    fn()
    const end = performance.now()
    console.log(`${name} took ${end - start} milliseconds`)
  } else {
    fn()
  }
}

// Web Vitals monitoring (simplified)
export const reportWebVitals = (metric: any): void => {
  if (process.env.NODE_ENV === 'production') {
    // In production, you would send this to your analytics service
    console.log(metric)
  }
}

// Service Worker registration
export const registerServiceWorker = async (): Promise<void> => {
  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      console.log('SW registered: ', registration)
    } catch (registrationError) {
      console.log('SW registration failed: ', registrationError)
    }
  }
}

// Resource hints for better loading
export const addResourceHints = (): void => {
  // DNS prefetch for external domains
  const dnsPrefetch = ['fonts.googleapis.com', 'fonts.gstatic.com']
  dnsPrefetch.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'dns-prefetch'
    link.href = `//${domain}`
    document.head.appendChild(link)
  })
  
  // Preconnect to critical domains
  const preconnect = ['fonts.googleapis.com']
  preconnect.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = `https://${domain}`
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
  })
}

// Initialize performance optimizations
export const initPerformanceOptimizations = (): void => {
  addResourceHints()
  registerServiceWorker()
}

export default {
  debounce,
  throttle,
  createIntersectionObserver,
  memoize,
  storage,
  getOptimizedImageUrl,
  preloadResource,
  prefetchResource,
  measurePerformance,
  reportWebVitals,
  initPerformanceOptimizations
}
