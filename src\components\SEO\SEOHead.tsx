import React from 'react'
import { Helmet } from 'react-helmet-async'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product'
  price?: number
  currency?: string
  availability?: 'in_stock' | 'out_of_stock' | 'preorder'
  brand?: string
  category?: string
  noIndex?: boolean
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description = 'متجر الملابس الرائد في المملكة العربية السعودية. تسوق أحدث صيحات الموضة للرجال والنساء بأفضل الأسعار وجودة عالية.',
  keywords = 'ملابس، موضة، تسوق، السعودية، رجالي، نسائي، أزياء، تخفيضات',
  image = '/images/og-image.jpg',
  url,
  type = 'website',
  price,
  currency = 'SAR',
  availability = 'in_stock',
  brand = 'متجر الملابس',
  category,
  noIndex = false
}) => {
  const siteTitle = 'متجر الملابس'
  const fullTitle = title ? `${title} | ${siteTitle}` : siteTitle
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '')

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Language and Direction */}
      <html lang="ar" dir="rtl" />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:site_name" content={siteTitle} />
      <meta property="og:locale" content="ar_SA" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* Product-specific meta tags */}
      {type === 'product' && (
        <>
          <meta property="product:brand" content={brand} />
          <meta property="product:availability" content={availability} />
          {price && (
            <>
              <meta property="product:price:amount" content={price.toString()} />
              <meta property="product:price:currency" content={currency} />
            </>
          )}
          {category && <meta property="product:category" content={category} />}
        </>
      )}
      
      {/* Structured Data for Products */}
      {type === 'product' && price && (
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": title,
            "description": description,
            "image": image,
            "brand": {
              "@type": "Brand",
              "name": brand
            },
            "offers": {
              "@type": "Offer",
              "price": price,
              "priceCurrency": currency,
              "availability": `https://schema.org/${availability === 'in_stock' ? 'InStock' : 'OutOfStock'}`,
              "url": currentUrl
            },
            ...(category && { "category": category })
          })}
        </script>
      )}
      
      {/* Structured Data for Website */}
      {type === 'website' && (
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": siteTitle,
            "description": description,
            "url": currentUrl,
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${currentUrl}/products?search={search_term_string}`
              },
              "query-input": "required name=search_term_string"
            }
          })}
        </script>
      )}
      
      {/* Favicon and App Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      
      {/* Manifest */}
      <link rel="manifest" href="/manifest.json" />
      
      {/* Theme Color */}
      <meta name="theme-color" content="#000000" />
      <meta name="msapplication-TileColor" content="#000000" />
      
      {/* Viewport */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
      
      {/* Canonical URL */}
      {currentUrl && <link rel="canonical" href={currentUrl} />}
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
    </Helmet>
  )
}

export default SEOHead
