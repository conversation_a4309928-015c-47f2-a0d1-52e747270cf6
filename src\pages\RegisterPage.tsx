import React from 'react'
import styled from 'styled-components'
import RegisterForm from '../components/Auth/RegisterForm'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: ${({ theme }) => theme.spacing.lg} ${({ theme }) => theme.spacing.md};
  }
`

const RegisterPage: React.FC = () => {
  return (
    <Container>
      <RegisterForm />
    </Container>
  )
}

export default RegisterPage
