import React, { useState, useMemo } from 'react'
import styled from 'styled-components'
import { Search, Filter, Grid, List } from 'lucide-react'
import { useProducts } from '../context/ProductContext'
import ProductCard from '../components/Product/ProductCard'
import Breadcrumb from '../components/UI/Breadcrumb'
import Input from '../components/UI/Input'
import Select from '../components/UI/Select'
import Button from '../components/UI/Button'
import Pagination from '../components/UI/Pagination'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: 0 ${({ theme }) => theme.spacing.md};
  }
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const ResultsCount = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const FiltersSection = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: column;
  }
`

const SearchWrapper = styled.div`
  flex: 1;
  position: relative;
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.light};
  width: 18px;
  height: 18px;
  pointer-events: none;
`

const FilterControls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`

const ViewToggle = styled.div`
  display: flex;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  overflow: hidden;
`

const ViewButton = styled.button<{ $active: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing.sm};
  border: none;
  background-color: ${({ $active, theme }) =>
    $active ? theme.colors.primary : theme.colors.background};
  color: ${({ $active, theme }) =>
    $active ? theme.colors.text.white : theme.colors.text.primary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover:not(:disabled) {
    background-color: ${({ $active, theme }) =>
      $active ? theme.colors.accent : theme.colors.surface};
  }
`

const ProductsGrid = styled.div<{ $viewMode: string }>`
  display: grid;
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  ${({ $viewMode }) => {
    if ($viewMode === 'list') {
      return 'grid-template-columns: 1fr;'
    }
    return `
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    `
  }}
`

const NoResults = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.secondary};
`

const NoResultsTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`

const ITEMS_PER_PAGE = 12

const ProductsPage: React.FC = () => {
  const { products } = useProducts()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [currentPage, setCurrentPage] = useState(1)

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'المنتجات', current: true }
  ]

  // Get unique categories
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(products.map(p => p.category))]
    return uniqueCategories.map(cat => ({ value: cat, label: cat }))
  }, [products])

  // Filter and sort products
  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = !selectedCategory || product.category === selectedCategory

      return matchesSearch && matchesCategory
    })

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return (a.salePrice || a.price) - (b.salePrice || b.price)
        case 'price-high':
          return (b.salePrice || b.price) - (a.salePrice || a.price)
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        default:
          return a.name.localeCompare(b.name, 'ar')
      }
    })

    return filtered
  }, [products, searchTerm, selectedCategory, sortBy])

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + ITEMS_PER_PAGE)

  const sortOptions = [
    { value: 'name', label: 'الاسم (أ-ي)' },
    { value: 'price-low', label: 'السعر (من الأقل للأعلى)' },
    { value: 'price-high', label: 'السعر (من الأعلى للأقل)' },
    { value: 'newest', label: 'الأحدث' }
  ]

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <PageHeader>
        <div>
          <Title>المنتجات</Title>
          <ResultsCount>
            {filteredProducts.length} {filteredProducts.length === 1 ? 'منتج' : 'منتجات'}
          </ResultsCount>
        </div>

        <ViewToggle>
          <ViewButton
            $active={viewMode === 'grid'}
            onClick={() => setViewMode('grid')}
            aria-label="عرض شبكي"
          >
            <Grid size={18} />
          </ViewButton>
          <ViewButton
            $active={viewMode === 'list'}
            onClick={() => setViewMode('list')}
            aria-label="عرض قائمة"
          >
            <List size={18} />
          </ViewButton>
        </ViewToggle>
      </PageHeader>

      <FiltersSection>
        <SearchWrapper>
          <Input
            type="text"
            placeholder="البحث في المنتجات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />
          <SearchIcon />
        </SearchWrapper>

        <FilterControls>
          <Select
            placeholder="جميع الفئات"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            options={[
              { value: '', label: 'جميع الفئات' },
              ...categories
            ]}
          />

          <Select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            options={sortOptions}
          />
        </FilterControls>
      </FiltersSection>

      {paginatedProducts.length === 0 ? (
        <NoResults>
          <NoResultsTitle>لم يتم العثور على منتجات</NoResultsTitle>
          <p>جرب تغيير معايير البحث أو الفلترة</p>
        </NoResults>
      ) : (
        <>
          <ProductsGrid $viewMode={viewMode}>
            {paginatedProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </ProductsGrid>

          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          )}
        </>
      )}
    </Container>
  )
}

export default ProductsPage
