import React from 'react'
import { Link } from 'react-router-dom'
import styled from 'styled-components'
import { Phone, Mail, MapPin } from 'lucide-react'

const FooterContainer = styled.footer`
  background-color: ${({ theme }) => theme.colors.primary};
  color: ${({ theme }) => theme.colors.text.white};
  padding: ${({ theme }) => theme.spacing.xxl} 0 ${({ theme }) => theme.spacing.lg};
  margin-top: auto;
`

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: 0 ${({ theme }) => theme.spacing.md};
  }
`

const FooterContent = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing.xl};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing.lg};
  }
`

const FooterSection = styled.div`
  h3 {
    font-size: 1.25rem;
    font-weight: ${({ theme }) => theme.fonts.weights.bold};
    margin-bottom: ${({ theme }) => theme.spacing.lg};
    color: ${({ theme }) => theme.colors.text.white};
  }
`

const FooterLink = styled(Link)`
  display: block;
  color: ${({ theme }) => theme.colors.text.white};
  text-decoration: none;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  opacity: 0.8;
  transition: opacity ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    opacity: 1;
  }
`

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  opacity: 0.8;
  
  svg {
    width: 16px;
    height: 16px;
  }
`

const Copyright = styled.div`
  text-align: center;
  padding-top: ${({ theme }) => theme.spacing.lg};
  border-top: 1px solid ${({ theme }) => theme.colors.text.white}20;
  opacity: 0.6;
  font-size: 0.875rem;
`

const Footer: React.FC = () => {
  return (
    <FooterContainer>
      <Container>
        <FooterContent>
          <FooterSection>
            <h3>متجر الملابس العربي</h3>
            <p style={{ opacity: 0.8, lineHeight: 1.6 }}>
              متجرك المفضل للأزياء العصرية والملابس عالية الجودة. 
              نقدم لك أفضل التصاميم بأسعار مناسبة وخدمة عملاء متميزة.
            </p>
          </FooterSection>

          <FooterSection>
            <h3>روابط سريعة</h3>
            <FooterLink to="/">الرئيسية</FooterLink>
            <FooterLink to="/products">المنتجات</FooterLink>
            <FooterLink to="/about">من نحن</FooterLink>
            <FooterLink to="/contact">اتصل بنا</FooterLink>
            <FooterLink to="/cart">سلة التسوق</FooterLink>
            <FooterLink to="/wishlist">المفضلة</FooterLink>
          </FooterSection>

          <FooterSection>
            <h3>الفئات</h3>
            <FooterLink to="/category/shirts">قمصان</FooterLink>
            <FooterLink to="/category/pants">بناطيل</FooterLink>
            <FooterLink to="/category/dresses">فساتين</FooterLink>
            <FooterLink to="/category/jackets">جاكيتات</FooterLink>
            <FooterLink to="/category/shoes">أحذية</FooterLink>
            <FooterLink to="/category/accessories">إكسسوارات</FooterLink>
          </FooterSection>

          <FooterSection>
            <h3>تواصل معنا</h3>
            <ContactItem>
              <Phone />
              <span>+966 50 123 4567</span>
            </ContactItem>
            <ContactItem>
              <Mail />
              <span><EMAIL></span>
            </ContactItem>
            <ContactItem>
              <MapPin />
              <span>الرياض، المملكة العربية السعودية</span>
            </ContactItem>
          </FooterSection>
        </FooterContent>

        <Copyright>
          <p>© 2024 متجر الملابس العربي. جميع الحقوق محفوظة.</p>
        </Copyright>
      </Container>
    </FooterContainer>
  )
}

export default Footer
