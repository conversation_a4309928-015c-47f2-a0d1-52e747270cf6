import React from 'react'
import styled, { css } from 'styled-components'
import type { ReactNode } from 'react'

interface BadgeProps {
  children: ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  rounded?: boolean
  className?: string
}

const getVariantStyles = (variant: string) => {
  switch (variant) {
    case 'primary':
      return css`
        background-color: ${({ theme }) => theme.colors.primary};
        color: ${({ theme }) => theme.colors.text.white};
      `
    case 'secondary':
      return css`
        background-color: ${({ theme }) => theme.colors.surface};
        color: ${({ theme }) => theme.colors.text.primary};
        border: 1px solid ${({ theme }) => theme.colors.border.medium};
      `
    case 'success':
      return css`
        background-color: ${({ theme }) => theme.colors.status.success};
        color: ${({ theme }) => theme.colors.text.white};
      `
    case 'warning':
      return css`
        background-color: ${({ theme }) => theme.colors.status.warning};
        color: ${({ theme }) => theme.colors.text.primary};
      `
    case 'error':
      return css`
        background-color: ${({ theme }) => theme.colors.status.error};
        color: ${({ theme }) => theme.colors.text.white};
      `
    case 'info':
      return css`
        background-color: ${({ theme }) => theme.colors.status.info};
        color: ${({ theme }) => theme.colors.text.white};
      `
    default:
      return css`
        background-color: ${({ theme }) => theme.colors.primary};
        color: ${({ theme }) => theme.colors.text.white};
      `
  }
}

const getSizeStyles = (size: string) => {
  switch (size) {
    case 'sm':
      return css`
        padding: 2px 6px;
        font-size: 0.75rem;
        min-height: 18px;
      `
    case 'lg':
      return css`
        padding: 6px 12px;
        font-size: 1rem;
        min-height: 32px;
      `
    default:
      return css`
        padding: 4px 8px;
        font-size: 0.875rem;
        min-height: 24px;
      `
  }
}

const StyledBadge = styled.span<{
  $variant: string
  $size: string
  $rounded: boolean
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: ${({ theme }) => theme.fonts.primary};
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  white-space: nowrap;
  vertical-align: middle;
  border-radius: ${({ $rounded, theme }) => 
    $rounded ? theme.borderRadius.full : theme.borderRadius.sm};
  
  ${({ $variant }) => getVariantStyles($variant)}
  ${({ $size }) => getSizeStyles($size)}
`

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  rounded = false,
  className,
  ...props
}) => {
  return (
    <StyledBadge
      $variant={variant}
      $size={size}
      $rounded={rounded}
      className={className}
      {...props}
    >
      {children}
    </StyledBadge>
  )
}

export default Badge
