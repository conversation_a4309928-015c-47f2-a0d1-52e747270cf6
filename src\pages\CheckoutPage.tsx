import React, { useState } from 'react'
import styled from 'styled-components'
import { CreditCard, MapPin, Package, CheckCircle, ArrowLeft, ArrowRight } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useCart } from '../context/CartContext'
import { useAuth } from '../context/AuthContext'
import { formatPrice } from '../utils/formatters'
import Breadcrumb from '../components/UI/Breadcrumb'
import Button from '../components/UI/Button'
import Input from '../components/UI/Input'
import Select from '../components/UI/Select'
import Card from '../components/UI/Card'
import Alert from '../components/UI/Alert'
import Stepper from '../components/UI/Stepper'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const CheckoutGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing.lg};
  }
`

const MainContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`

const StepCard = styled(Card)<{ $active: boolean }>`
  opacity: ${({ $active }) => $active ? 1 : 0.6};
  pointer-events: ${({ $active }) => $active ? 'auto' : 'none'};
  transition: opacity ${({ theme }) => theme.transitions.fast};
`

const StepHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const StepIcon = styled.div<{ $variant: 'primary' | 'success' }>`
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme, $variant }) =>
    $variant === 'success' ? theme.colors.status.success : theme.colors.primary};
  color: white;
`

const StepTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.md};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`

const FormField = styled.div<{ $fullWidth?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};

  ${({ $fullWidth }) => $fullWidth && `
    grid-column: 1 / -1;
  `}
`

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const PaymentMethodGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const PaymentMethod = styled.div<{ $selected: boolean }>`
  padding: ${({ theme }) => theme.spacing.md};
  border: 2px solid ${({ theme, $selected }) =>
    $selected ? theme.colors.primary : theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  background-color: ${({ theme, $selected }) =>
    $selected ? theme.colors.primary + '10' : theme.colors.background};

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary};
  }
`

const PaymentMethodIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background-color: ${({ theme }) => theme.colors.surface};
  margin: 0 auto ${({ theme }) => theme.spacing.sm};
`

const PaymentMethodName = styled.div`
  text-align: center;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const StepActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing.lg};
  padding-top: ${({ theme }) => theme.spacing.lg};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`

const OrderSummary = styled(Card)`
  position: sticky;
  top: ${({ theme }) => theme.spacing.lg};
  height: fit-content;
`

const SummaryTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
`

const OrderItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }
`

const ItemImage = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ItemInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ItemName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const ItemDetails = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const ItemPrice = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const SummaryRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.sm};

  &:last-child {
    margin-bottom: 0;
    padding-top: ${({ theme }) => theme.spacing.md};
    border-top: 1px solid ${({ theme }) => theme.colors.border.light};
    font-weight: ${({ theme }) => theme.fonts.weights.bold};
    font-size: 1.125rem;
  }
`

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate()
  const { items, total, clearCart } = useCart()
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [orderPlaced, setOrderPlaced] = useState(false)
  const [errors, setErrors] = useState<string[]>([])

  const [shippingInfo, setShippingInfo] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    country: 'السعودية'
  })

  const [paymentInfo, setPaymentInfo] = useState({
    method: 'card',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: ''
  })

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'السلة', href: '/cart' },
    { label: 'الدفع', current: true }
  ]

  const steps = [
    { id: 1, title: 'معلومات الشحن', icon: MapPin },
    { id: 2, title: 'طريقة الدفع', icon: CreditCard },
    { id: 3, title: 'مراجعة الطلب', icon: Package },
    { id: 4, title: 'تأكيد الطلب', icon: CheckCircle }
  ]

  const paymentMethods = [
    { id: 'card', name: 'بطاقة ائتمان', icon: CreditCard },
    { id: 'cash', name: 'الدفع عند الاستلام', icon: Package }
  ]

  const shippingCost = 25
  const tax = total * 0.15 // 15% VAT
  const finalTotal = total + shippingCost + tax

  const handleShippingInfoChange = (field: string, value: string) => {
    setShippingInfo(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePaymentInfoChange = (field: string, value: string) => {
    setPaymentInfo(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateStep = (step: number) => {
    const newErrors: string[] = []

    if (step === 1) {
      if (!shippingInfo.firstName.trim()) newErrors.push('الاسم الأول مطلوب')
      if (!shippingInfo.lastName.trim()) newErrors.push('الاسم الأخير مطلوب')
      if (!shippingInfo.email.trim()) newErrors.push('البريد الإلكتروني مطلوب')
      if (!shippingInfo.phone.trim()) newErrors.push('رقم الهاتف مطلوب')
      if (!shippingInfo.address.trim()) newErrors.push('العنوان مطلوب')
      if (!shippingInfo.city.trim()) newErrors.push('المدينة مطلوبة')
      if (!shippingInfo.postalCode.trim()) newErrors.push('الرمز البريدي مطلوب')
    }

    if (step === 2 && paymentInfo.method === 'card') {
      if (!paymentInfo.cardNumber.trim()) newErrors.push('رقم البطاقة مطلوب')
      if (!paymentInfo.expiryDate.trim()) newErrors.push('تاريخ انتهاء الصلاحية مطلوب')
      if (!paymentInfo.cvv.trim()) newErrors.push('رمز الأمان مطلوب')
      if (!paymentInfo.cardName.trim()) newErrors.push('اسم حامل البطاقة مطلوب')
    }

    setErrors(newErrors)
    return newErrors.length === 0
  }

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4))
    }
  }

  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handlePlaceOrder = () => {
    if (validateStep(currentStep)) {
      // Simulate order placement
      const orderId = 'ORD-2024-001'
      setTimeout(() => {
        clearCart()
        navigate(`/order-confirmation/${orderId}`)
      }, 1000)
    }
  }

  if (items.length === 0 && !orderPlaced) {
    return (
      <Container>
        <Alert variant="info">
          السلة فارغة. <a href="/products">تصفح المنتجات</a> لإضافة عناصر إلى السلة.
        </Alert>
      </Container>
    )
  }

  if (orderPlaced) {
    return (
      <Container>
        <Card padding="xl" style={{ textAlign: 'center', maxWidth: '600px', margin: '0 auto' }}>
          <CheckCircle size={64} color="#10B981" style={{ margin: '0 auto 1rem' }} />
          <h1 style={{ color: '#10B981', marginBottom: '1rem' }}>تم تأكيد طلبك!</h1>
          <p style={{ marginBottom: '2rem', color: '#6B7280' }}>
            شكراً لك على طلبك. سيتم إرسال تفاصيل الطلب إلى بريدك الإلكتروني.
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
            <Button variant="primary" onClick={() => window.location.href = '/orders'}>
              عرض طلباتي
            </Button>
            <Button variant="outline" onClick={() => window.location.href = '/products'}>
              متابعة التسوق
            </Button>
          </div>
        </Card>
      </Container>
    )
  }

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <div style={{ marginBottom: '2rem' }}>
        <Stepper
          steps={steps.map(step => ({
            title: step.title,
            completed: currentStep > step.id,
            active: currentStep === step.id
          }))}
        />
      </div>

      {errors.length > 0 && (
        <Alert variant="error" style={{ marginBottom: '1rem' }}>
          <ul style={{ margin: 0, paddingRight: '1rem' }}>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </Alert>
      )}

      <CheckoutGrid>
        <MainContent>
          {/* Step 1: Shipping Information */}
          <StepCard $active={currentStep === 1} padding="lg">
            <StepHeader>
              <StepIcon $variant={currentStep > 1 ? 'success' : 'primary'}>
                {currentStep > 1 ? <CheckCircle size={20} /> : <MapPin size={20} />}
              </StepIcon>
              <StepTitle>معلومات الشحن</StepTitle>
            </StepHeader>

            <FormGrid>
              <FormField>
                <Label>الاسم الأول *</Label>
                <Input
                  type="text"
                  value={shippingInfo.firstName}
                  onChange={(e) => handleShippingInfoChange('firstName', e.target.value)}
                  placeholder="الاسم الأول"
                />
              </FormField>

              <FormField>
                <Label>الاسم الأخير *</Label>
                <Input
                  type="text"
                  value={shippingInfo.lastName}
                  onChange={(e) => handleShippingInfoChange('lastName', e.target.value)}
                  placeholder="الاسم الأخير"
                />
              </FormField>

              <FormField>
                <Label>البريد الإلكتروني *</Label>
                <Input
                  type="email"
                  value={shippingInfo.email}
                  onChange={(e) => handleShippingInfoChange('email', e.target.value)}
                  placeholder="البريد الإلكتروني"
                />
              </FormField>

              <FormField>
                <Label>رقم الهاتف *</Label>
                <Input
                  type="tel"
                  value={shippingInfo.phone}
                  onChange={(e) => handleShippingInfoChange('phone', e.target.value)}
                  placeholder="رقم الهاتف"
                />
              </FormField>

              <FormField $fullWidth>
                <Label>العنوان *</Label>
                <Input
                  type="text"
                  value={shippingInfo.address}
                  onChange={(e) => handleShippingInfoChange('address', e.target.value)}
                  placeholder="العنوان الكامل"
                />
              </FormField>

              <FormField>
                <Label>المدينة *</Label>
                <Input
                  type="text"
                  value={shippingInfo.city}
                  onChange={(e) => handleShippingInfoChange('city', e.target.value)}
                  placeholder="المدينة"
                />
              </FormField>

              <FormField>
                <Label>الرمز البريدي *</Label>
                <Input
                  type="text"
                  value={shippingInfo.postalCode}
                  onChange={(e) => handleShippingInfoChange('postalCode', e.target.value)}
                  placeholder="الرمز البريدي"
                />
              </FormField>
            </FormGrid>

            {currentStep === 1 && (
              <StepActions>
                <div></div>
                <Button variant="primary" onClick={handleNextStep}>
                  التالي
                  <ArrowRight size={16} />
                </Button>
              </StepActions>
            )}
          </StepCard>

          {/* Step 2: Payment Method */}
          <StepCard $active={currentStep === 2} padding="lg">
            <StepHeader>
              <StepIcon $variant={currentStep > 2 ? 'success' : 'primary'}>
                {currentStep > 2 ? <CheckCircle size={20} /> : <CreditCard size={20} />}
              </StepIcon>
              <StepTitle>طريقة الدفع</StepTitle>
            </StepHeader>

            <PaymentMethodGrid>
              {paymentMethods.map((method) => (
                <PaymentMethod
                  key={method.id}
                  $selected={paymentInfo.method === method.id}
                  onClick={() => handlePaymentInfoChange('method', method.id)}
                >
                  <PaymentMethodIcon>
                    <method.icon size={24} />
                  </PaymentMethodIcon>
                  <PaymentMethodName>{method.name}</PaymentMethodName>
                </PaymentMethod>
              ))}
            </PaymentMethodGrid>

            {paymentInfo.method === 'card' && (
              <FormGrid>
                <FormField $fullWidth>
                  <Label>رقم البطاقة *</Label>
                  <Input
                    type="text"
                    value={paymentInfo.cardNumber}
                    onChange={(e) => handlePaymentInfoChange('cardNumber', e.target.value)}
                    placeholder="1234 5678 9012 3456"
                  />
                </FormField>

                <FormField>
                  <Label>تاريخ انتهاء الصلاحية *</Label>
                  <Input
                    type="text"
                    value={paymentInfo.expiryDate}
                    onChange={(e) => handlePaymentInfoChange('expiryDate', e.target.value)}
                    placeholder="MM/YY"
                  />
                </FormField>

                <FormField>
                  <Label>رمز الأمان *</Label>
                  <Input
                    type="text"
                    value={paymentInfo.cvv}
                    onChange={(e) => handlePaymentInfoChange('cvv', e.target.value)}
                    placeholder="123"
                  />
                </FormField>

                <FormField $fullWidth>
                  <Label>اسم حامل البطاقة *</Label>
                  <Input
                    type="text"
                    value={paymentInfo.cardName}
                    onChange={(e) => handlePaymentInfoChange('cardName', e.target.value)}
                    placeholder="الاسم كما هو مكتوب على البطاقة"
                  />
                </FormField>
              </FormGrid>
            )}

            {currentStep === 2 && (
              <StepActions>
                <Button variant="outline" onClick={handlePrevStep}>
                  <ArrowLeft size={16} />
                  السابق
                </Button>
                <Button variant="primary" onClick={handleNextStep}>
                  التالي
                  <ArrowRight size={16} />
                </Button>
              </StepActions>
            )}
          </StepCard>

          {/* Step 3: Order Review */}
          <StepCard $active={currentStep === 3} padding="lg">
            <StepHeader>
              <StepIcon $variant="primary">
                <Package size={20} />
              </StepIcon>
              <StepTitle>مراجعة الطلب</StepTitle>
            </StepHeader>

            <div style={{ marginBottom: '2rem' }}>
              <h4 style={{ marginBottom: '1rem' }}>معلومات الشحن:</h4>
              <p style={{ margin: 0, lineHeight: 1.6, color: '#6B7280' }}>
                {shippingInfo.firstName} {shippingInfo.lastName}<br />
                {shippingInfo.address}<br />
                {shippingInfo.city}, {shippingInfo.postalCode}<br />
                {shippingInfo.phone}
              </p>
            </div>

            <div style={{ marginBottom: '2rem' }}>
              <h4 style={{ marginBottom: '1rem' }}>طريقة الدفع:</h4>
              <p style={{ margin: 0, color: '#6B7280' }}>
                {paymentMethods.find(m => m.id === paymentInfo.method)?.name}
                {paymentInfo.method === 'card' && paymentInfo.cardNumber && (
                  <span> - **** **** **** {paymentInfo.cardNumber.slice(-4)}</span>
                )}
              </p>
            </div>

            {currentStep === 3 && (
              <StepActions>
                <Button variant="outline" onClick={handlePrevStep}>
                  <ArrowLeft size={16} />
                  السابق
                </Button>
                <Button variant="primary" onClick={handlePlaceOrder}>
                  تأكيد الطلب
                </Button>
              </StepActions>
            )}
          </StepCard>
        </MainContent>

        <OrderSummary padding="lg">
          <SummaryTitle>ملخص الطلب</SummaryTitle>

          {items.map((item) => (
            <OrderItem key={`${item.id}-${item.selectedSize}-${item.selectedColor}`}>
              <ItemImage src={item.images[0]} alt={item.name} />
              <ItemInfo>
                <ItemName>{item.name}</ItemName>
                <ItemDetails>
                  الكمية: {item.quantity} | المقاس: {item.selectedSize} | اللون: {item.selectedColor}
                </ItemDetails>
              </ItemInfo>
              <ItemPrice>{formatPrice(item.price * item.quantity)}</ItemPrice>
            </OrderItem>
          ))}

          <div style={{ marginTop: '1.5rem', paddingTop: '1.5rem', borderTop: '1px solid #E5E7EB' }}>
            <SummaryRow>
              <span>المجموع الفرعي:</span>
              <span>{formatPrice(total)}</span>
            </SummaryRow>
            <SummaryRow>
              <span>الشحن:</span>
              <span>{formatPrice(shippingCost)}</span>
            </SummaryRow>
            <SummaryRow>
              <span>الضريبة (15%):</span>
              <span>{formatPrice(tax)}</span>
            </SummaryRow>
            <SummaryRow>
              <span>المجموع الكلي:</span>
              <span>{formatPrice(finalTotal)}</span>
            </SummaryRow>
          </div>
        </OrderSummary>
      </CheckoutGrid>
    </Container>
  )
}

export default CheckoutPage
