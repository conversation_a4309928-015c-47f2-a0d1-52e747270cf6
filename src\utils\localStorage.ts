// Local Storage Utility Functions

export const STORAGE_KEYS = {
  CART: 'arabic_store_cart',
  WISHLIST: 'arabic_store_wishlist',
  USER: 'arabic_store_user',
  AUTH_TOKEN: 'arabic_store_auth_token',
  RECENT_SEARCHES: 'arabic_store_recent_searches',
  THEME_PREFERENCES: 'arabic_store_theme_preferences'
} as const

// Generic storage functions
export const storage = {
  get: <T>(key: string): T | null => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error(`Error reading from localStorage key "${key}":`, error)
      return null
    }
  },

  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error(`Error writing to localStorage key "${key}":`, error)
    }
  },

  remove: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error)
    }
  },

  clear: (): void => {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('Error clearing localStorage:', error)
    }
  }
}

// Cart-specific storage functions
export const cartStorage = {
  get: () => storage.get(STORAGE_KEYS.CART),
  set: (cart: any) => storage.set(STORAGE_KEYS.CART, cart),
  clear: () => storage.remove(STORAGE_KEYS.CART)
}

// Wishlist-specific storage functions
export const wishlistStorage = {
  get: () => storage.get(STORAGE_KEYS.WISHLIST),
  set: (wishlist: any) => storage.set(STORAGE_KEYS.WISHLIST, wishlist),
  clear: () => storage.remove(STORAGE_KEYS.WISHLIST)
}

// User-specific storage functions
export const userStorage = {
  get: () => storage.get(STORAGE_KEYS.USER),
  set: (user: any) => storage.set(STORAGE_KEYS.USER, user),
  clear: () => storage.remove(STORAGE_KEYS.USER)
}

// Auth token storage functions
export const authStorage = {
  get: () => storage.get<string>(STORAGE_KEYS.AUTH_TOKEN),
  set: (token: string) => storage.set(STORAGE_KEYS.AUTH_TOKEN, token),
  clear: () => storage.remove(STORAGE_KEYS.AUTH_TOKEN)
}

// Recent searches storage functions
export const recentSearchesStorage = {
  get: (): string[] => storage.get<string[]>(STORAGE_KEYS.RECENT_SEARCHES) || [],
  set: (searches: string[]) => storage.set(STORAGE_KEYS.RECENT_SEARCHES, searches),
  add: (search: string) => {
    const searches = recentSearchesStorage.get()
    const filteredSearches = searches.filter(s => s !== search)
    const newSearches = [search, ...filteredSearches].slice(0, 10) // Keep only last 10 searches
    recentSearchesStorage.set(newSearches)
  },
  clear: () => storage.remove(STORAGE_KEYS.RECENT_SEARCHES)
}

// Check if localStorage is available
export const isStorageAvailable = (): boolean => {
  try {
    const test = '__storage_test__'
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch {
    return false
  }
}

// Migrate old storage keys if needed
export const migrateStorage = (): void => {
  // Add migration logic here if storage structure changes
  console.log('Storage migration completed')
}
