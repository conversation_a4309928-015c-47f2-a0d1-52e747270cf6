import React from 'react'
import styled from 'styled-components'
import { Link } from 'react-router-dom'
import Button from '../components/UI/Button'
import SEOHead from '../components/SEO/SEOHead'

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: 0 ${({ theme }) => theme.spacing.md};
  }
`

const HeroSection = styled.section`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xxxl} 0;
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.surface} 0%, ${({ theme }) => theme.colors.background} 100%);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xxl};
`

const HeroTitle = styled.h1`
  font-size: 3rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  line-height: 1.2;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    font-size: 2rem;
  }
`

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    font-size: 1rem;
  }
`

const CategoriesSection = styled.section`
  margin-bottom: ${({ theme }) => theme.spacing.xxl};
`

const SectionTitle = styled.h2`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.primary};
`

const CategoriesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    grid-template-columns: repeat(2, 1fr);
    gap: ${({ theme }) => theme.spacing.md};
  }
`

const CategoryCard = styled(Link)`
  display: block;
  padding: ${({ theme }) => theme.spacing.xl};
  background: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  text-align: center;
  text-decoration: none;
  color: ${({ theme }) => theme.colors.text.primary};
  transition: all ${({ theme }) => theme.transitions.normal};
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
    border-color: ${({ theme }) => theme.colors.primary};
  }
`

const CategoryIcon = styled.div`
  font-size: 2rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const CategoryName = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`

const CategoryDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const FeaturesSection = styled.section`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing.xl};
  margin-bottom: ${({ theme }) => theme.spacing.xxl};
`

const FeatureCard = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.lg};
`

const FeatureIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.primary};
`

const FeatureTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.text.primary};
`

const FeatureDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  line-height: 1.6;
`

const HomePage: React.FC = () => {
  const categories = [
    { name: 'قمصان', slug: 'shirts', icon: '👔', description: 'قمصان رجالية ونسائية عصرية' },
    { name: 'بناطيل', slug: 'pants', icon: '👖', description: 'بناطيل بتصاميم متنوعة' },
    { name: 'فساتين', slug: 'dresses', icon: '👗', description: 'فساتين أنيقة لجميع المناسبات' },
    { name: 'جاكيتات', slug: 'jackets', icon: '🧥', description: 'جاكيتات شتوية وصيفية' },
    { name: 'أحذية', slug: 'shoes', icon: '👟', description: 'أحذية رياضية وكلاسيكية' },
    { name: 'إكسسوارات', slug: 'accessories', icon: '👜', description: 'حقائب وإكسسوارات متنوعة' }
  ]

  const features = [
    {
      icon: '🚚',
      title: 'شحن مجاني',
      description: 'شحن مجاني لجميع الطلبات أكثر من 200 ريال'
    },
    {
      icon: '💳',
      title: 'دفع آمن',
      description: 'طرق دفع متعددة وآمنة لراحتك'
    },
    {
      icon: '🔄',
      title: 'إرجاع مجاني',
      description: 'إمكانية الإرجاع خلال 30 يوم من الشراء'
    },
    {
      icon: '📞',
      title: 'دعم 24/7',
      description: 'خدمة عملاء متاحة على مدار الساعة'
    }
  ]

  return (
    <>
      <SEOHead
        title="متجر الملابس العربي - أحدث صيحات الموضة"
        description="اكتشف أحدث صيحات الموضة والأزياء العصرية في متجر الملابس العربي. تسوق قمصان، فساتين، بناطيل، جاكيتات وأكثر بأفضل الأسعار وجودة عالية."
        keywords="ملابس، موضة، أزياء، تسوق، قمصان، فساتين، بناطيل، جاكيتات، أحذية، إكسسوارات، السعودية"
        type="website"
      />
      <Container>
      <HeroSection>
        <HeroTitle>مرحباً بك في متجر الملابس العربي</HeroTitle>
        <HeroSubtitle>
          اكتشف أحدث صيحات الموضة والأزياء العصرية بأفضل الأسعار. 
          تسوق الآن واستمتع بتجربة تسوق فريدة ومميزة.
        </HeroSubtitle>
        <Button as={Link} to="/products" size="lg">
          تسوق الآن
        </Button>
      </HeroSection>

      <CategoriesSection>
        <SectionTitle>تسوق حسب الفئة</SectionTitle>
        <CategoriesGrid>
          {categories.map((category) => (
            <CategoryCard key={category.slug} to={`/category/${category.slug}`}>
              <CategoryIcon>{category.icon}</CategoryIcon>
              <CategoryName>{category.name}</CategoryName>
              <CategoryDescription>{category.description}</CategoryDescription>
            </CategoryCard>
          ))}
        </CategoriesGrid>
      </CategoriesSection>

      <FeaturesSection>
        {features.map((feature, index) => (
          <FeatureCard key={index}>
            <FeatureIcon>{feature.icon}</FeatureIcon>
            <FeatureTitle>{feature.title}</FeatureTitle>
            <FeatureDescription>{feature.description}</FeatureDescription>
          </FeatureCard>
        ))}
      </FeaturesSection>
    </Container>
    </>
  )
}

export default HomePage
