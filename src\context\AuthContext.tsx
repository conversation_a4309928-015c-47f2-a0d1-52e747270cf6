import React, { createContext, useContext, useReducer, useEffect } from 'react'
import type { ReactNode } from 'react'
import type { User, AuthState, LoginForm, RegisterForm } from '../types'
import { userStorage, authStorage } from '../utils/localStorage'

// Auth Actions
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }

// Auth Context Type
interface AuthContextType extends AuthState {
  login: (credentials: LoginForm) => Promise<void>
  register: (userData: RegisterForm) => Promise<void>
  logout: () => void
  updateUser: (userData: Partial<User>) => void
  loginAsGuest: () => void
}

// Initial State
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true
}

// Auth Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      }
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false
      }
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false
      }
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null
      }
    default:
      return state
  }
}

// Create Context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth Provider Component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUser = userStorage.get() as User | null
        const storedToken = authStorage.get()

        if (storedUser && storedToken) {
          dispatch({ type: 'LOGIN_SUCCESS', payload: storedUser })
        } else {
          dispatch({ type: 'SET_LOADING', payload: false })
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    }

    initializeAuth()
  }, [])

  // Login function
  const login = async (credentials: LoginForm): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true })
    
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock user data - replace with actual API response
      const mockUser: User = {
        id: '1',
        email: credentials.email,
        name: credentials.email.includes('admin') ? 'مدير المتجر' : 'مستخدم تجريبي',
        role: credentials.email.includes('admin') ? 'store_owner' : 'customer',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Mock token - replace with actual token from API
      const mockToken = 'mock-jwt-token-' + Date.now()

      // Store in localStorage
      userStorage.set(mockUser)
      authStorage.set(mockToken)

      dispatch({ type: 'LOGIN_SUCCESS', payload: mockUser })
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false })
      throw new Error('فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.')
    }
  }

  // Register function
  const register = async (userData: RegisterForm): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true })
    
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock user data - replace with actual API response
      const mockUser: User = {
        id: Date.now().toString(),
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        role: 'customer',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Mock token - replace with actual token from API
      const mockToken = 'mock-jwt-token-' + Date.now()

      // Store in localStorage
      userStorage.set(mockUser)
      authStorage.set(mockToken)

      dispatch({ type: 'LOGIN_SUCCESS', payload: mockUser })
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false })
      throw new Error('فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.')
    }
  }

  // Logout function
  const logout = (): void => {
    userStorage.clear()
    authStorage.clear()
    dispatch({ type: 'LOGOUT' })
  }

  // Update user function
  const updateUser = (userData: Partial<User>): void => {
    if (state.user) {
      const updatedUser = { ...state.user, ...userData, updatedAt: new Date() }
      userStorage.set(updatedUser)
      dispatch({ type: 'UPDATE_USER', payload: userData })
    }
  }

  // Login as guest function
  const loginAsGuest = (): void => {
    dispatch({ type: 'SET_LOADING', payload: false })
  }

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    updateUser,
    loginAsGuest
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
