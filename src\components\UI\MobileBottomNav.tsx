import React from 'react'
import styled from 'styled-components'
import { Link, useLocation } from 'react-router-dom'
import { Home, Search, ShoppingCart, Heart, User } from 'lucide-react'
import { useCart } from '../../context/CartContext'
import { useAuth } from '../../context/AuthContext'

interface NavItem {
  path: string
  icon: React.ReactNode
  label: string
  badge?: number
}

const BottomNavContainer = styled.nav`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: ${({ theme }) => theme.colors.background};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: ${({ theme }) => theme.spacing.sm} 0;
  z-index: 1000;
  display: none;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block;
  }
`

const NavList = styled.div`
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 500px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.md};
`

const NavItem = styled(Link)<{ $isActive: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  padding: ${({ theme }) => theme.spacing.xs};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  text-decoration: none;
  color: ${({ $isActive, theme }) => 
    $isActive ? theme.colors.primary : theme.colors.text.secondary};
  transition: all ${({ theme }) => theme.transitions.fast};
  position: relative;
  min-width: 60px;
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary};
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  &:active {
    transform: scale(0.95);
  }
`

const IconContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
`

const Badge = styled.span`
  position: absolute;
  top: -6px;
  left: -6px;
  background-color: ${({ theme }) => theme.colors.status.error};
  color: white;
  font-size: 0.75rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  padding: 2px 6px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
`

const Label = styled.span`
  font-size: 0.75rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  text-align: center;
  line-height: 1;
`

const MobileBottomNav: React.FC = () => {
  const location = useLocation()
  const { itemCount } = useCart()
  const { user } = useAuth()

  const cartItemsCount = itemCount
  
  const navItems: NavItem[] = [
    {
      path: '/',
      icon: <Home size={20} />,
      label: 'الرئيسية'
    },
    {
      path: '/products',
      icon: <Search size={20} />,
      label: 'البحث'
    },
    {
      path: '/cart',
      icon: <ShoppingCart size={20} />,
      label: 'السلة',
      badge: cartItemsCount
    },
    {
      path: '/wishlist',
      icon: <Heart size={20} />,
      label: 'المفضلة'
    },
    {
      path: user ? '/profile' : '/login',
      icon: <User size={20} />,
      label: user ? 'الحساب' : 'دخول'
    }
  ]
  
  const isActivePath = (path: string) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }
  
  return (
    <BottomNavContainer>
      <NavList>
        {navItems.map((item) => (
          <NavItem
            key={item.path}
            to={item.path}
            $isActive={isActivePath(item.path)}
          >
            <IconContainer>
              {item.icon}
              {item.badge && item.badge > 0 && (
                <Badge>{item.badge > 99 ? '99+' : item.badge}</Badge>
              )}
            </IconContainer>
            <Label>{item.label}</Label>
          </NavItem>
        ))}
      </NavList>
    </BottomNavContainer>
  )
}

export default MobileBottomNav
