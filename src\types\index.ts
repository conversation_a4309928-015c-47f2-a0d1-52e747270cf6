// User and Authentication Types
export interface User {
  id: string
  email: string
  name: string
  phone?: string
  role: 'customer' | 'store_owner'
  address?: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  createdAt: Date
  updatedAt: Date
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

// Product Types
export interface Product {
  id: string
  name: string
  description: string
  category: string
  subcategory?: string
  images: string[]
  price: number
  salePrice?: number
  stock?: number
  sku: string
  status: 'active' | 'inactive' | 'draft'
  sizes?: string[]
  colors?: string[]
  tags?: string[]
  rating?: number
  reviewCount?: number
  createdAt: Date
  updatedAt: Date
}

export interface ProductFilter {
  category?: string
  subcategory?: string
  minPrice?: number
  maxPrice?: number
  sizes?: string[]
  colors?: string[]
  tags?: string[]
  isOnSale?: boolean
  sortBy?: 'name' | 'price' | 'rating' | 'newest'
  sortOrder?: 'asc' | 'desc'
}

// Cart Types
export interface CartItem {
  id: string
  productId: string
  quantity: number
  size?: string
  color?: string
  price: number
}

export interface Cart {
  items: CartItem[]
  total: number
  itemCount: number
}

// Wishlist Types
export interface WishlistItem {
  id: string
  productId: string
  product: Product
  addedAt: Date
}

export interface Wishlist {
  items: WishlistItem[]
  itemCount: number
}

// Order Types
export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'

export interface OrderItem {
  productId: string
  productName: string
  quantity: number
  price: number
  size?: string
  color?: string
}

export interface Order {
  id: string
  customerId?: string
  customerEmail: string
  customerName: string
  customerPhone: string
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping: number
  total: number
  status: OrderStatus
  paymentMethod: 'credit_card' | 'cash_on_delivery' | 'bank_transfer'
  paymentStatus: 'pending' | 'paid' | 'failed'
  shippingAddress: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  notes?: string
  trackingNumber?: string
  createdAt: Date
  updatedAt: Date
}

// Discount Types
export interface Discount {
  id: string
  code: string
  type: 'percentage' | 'fixed'
  value: number
  minOrderAmount?: number
  maxDiscountAmount?: number
  isActive: boolean
  validFrom: Date
  validTo: Date
  usageLimit?: number
  usageCount: number
  createdAt: Date
}

// Review Types
export interface Review {
  id: string
  productId: string
  customerId?: string
  customerName: string
  rating: number
  comment: string
  isVerified: boolean
  createdAt: Date
}

// Category Types
export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  image?: string
  parentId?: string
  isActive: boolean
  sortOrder: number
}

// Analytics Types
export interface SalesAnalytics {
  totalSales: number
  totalOrders: number
  averageOrderValue: number
  topProducts: Array<{
    product: Product
    salesCount: number
    revenue: number
  }>
  salesByMonth: Array<{
    month: string
    sales: number
    orders: number
  }>
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// Form Types
export interface ContactForm {
  name: string
  email: string
  phone: string
  subject: string
  message: string
}

export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  name: string
  email: string
  password: string
  confirmPassword: string
  phone?: string
}

export interface CheckoutForm {
  customerName: string
  customerEmail: string
  customerPhone: string
  shippingAddress: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  paymentMethod: 'cash_on_delivery' | 'bank_transfer'
  notes?: string
  discountCode?: string
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

export interface PaginationParams {
  page: number
  limit: number
  total: number
  totalPages: number
}
