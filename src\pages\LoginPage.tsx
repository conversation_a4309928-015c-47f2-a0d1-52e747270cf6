import React from 'react'
import styled from 'styled-components'
import LoginForm from '../components/Auth/LoginForm'

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing.lg};
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: ${({ theme }) => theme.spacing.md};
    min-height: calc(100vh - 60px); /* Account for mobile nav */
  }
`

const LoginPage: React.FC = () => {
  return (
    <Container>
      <LoginForm />
    </Container>
  )
}

export default LoginPage
