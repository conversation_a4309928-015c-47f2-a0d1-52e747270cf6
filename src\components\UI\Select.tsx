import React, { forwardRef } from 'react'
import styled, { css } from 'styled-components'
import { ChevronDown } from 'lucide-react'

interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'children'> {
  label?: string
  error?: string
  fullWidth?: boolean
  variant?: 'default' | 'filled'
  options: SelectOption[]
  placeholder?: string
}

const SelectContainer = styled.div<{ $fullWidth: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
  
  ${({ $fullWidth }) =>
    $fullWidth &&
    css`
      width: 100%;
    `}
`

const Label = styled.label`
  font-size: 0.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const SelectWrapper = styled.div`
  position: relative;
  display: inline-block;
  width: 100%;
`

const StyledSelect = styled.select<{
  $hasError: boolean
  $variant: string
}>`
  width: 100%;
  padding: ${({ theme }) => theme.spacing.md};
  padding-left: 40px;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-family: ${({ theme }) => theme.fonts.primary};
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  transition: all ${({ theme }) => theme.transitions.fast};
  direction: rtl;
  text-align: right;
  appearance: none;
  cursor: pointer;
  
  ${({ $variant, theme }) =>
    $variant === 'filled' &&
    css`
      background-color: ${theme.colors.surface};
    `}
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary}20;
  }
  
  &:disabled {
    background-color: ${({ theme }) => theme.colors.surface};
    color: ${({ theme }) => theme.colors.text.light};
    cursor: not-allowed;
  }
  
  ${({ $hasError, theme }) =>
    $hasError &&
    css`
      border-color: ${theme.colors.status.error};
      
      &:focus {
        border-color: ${theme.colors.status.error};
        box-shadow: 0 0 0 3px ${theme.colors.status.error}20;
      }
    `}
`

const ChevronIcon = styled(ChevronDown)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.light};
  width: 18px;
  height: 18px;
  pointer-events: none;
`

const ErrorMessage = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.status.error};
  margin-top: ${({ theme }) => theme.spacing.xs};
`

const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ 
    label, 
    error, 
    fullWidth = false, 
    variant = 'default', 
    options,
    placeholder,
    className, 
    ...props 
  }, ref) => {
    return (
      <SelectContainer $fullWidth={fullWidth} className={className}>
        {label && <Label>{label}</Label>}
        <SelectWrapper>
          <StyledSelect
            ref={ref}
            $hasError={!!error}
            $variant={variant}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </StyledSelect>
          <ChevronIcon />
        </SelectWrapper>
        {error && <ErrorMessage>{error}</ErrorMessage>}
      </SelectContainer>
    )
  }
)

Select.displayName = 'Select'

export default Select
