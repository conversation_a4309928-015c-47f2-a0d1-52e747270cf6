import React, { useState } from 'react'
import styled from 'styled-components'
import { User, Mail, Phone, MapPin, Edit2, Save, X } from 'lucide-react'
import { useAuth } from '../../context/AuthContext'
import Breadcrumb from '../../components/UI/Breadcrumb'
import Button from '../../components/UI/Button'
import Input from '../../components/UI/Input'
import Card from '../../components/UI/Card'
import Alert from '../../components/UI/Alert'

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  padding: ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
`

const Avatar = styled.div`
  width: 80px;
  height: 80px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  background-color: ${({ theme }) => theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.text.white};
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
`

const UserInfo = styled.div`
  flex: 1;
`

const UserName = styled.h1`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
`

const UserEmail = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0;
`

const ProfileSection = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const SectionTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.md};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
  }
`

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const ReadOnlyValue = styled.div`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  color: ${({ theme }) => theme.colors.text.secondary};
  min-height: 44px;
  display: flex;
  align-items: center;
`

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-top: ${({ theme }) => theme.spacing.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
  }
`

const ProfilePage: React.FC = () => {
  const { user } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [showAlert, setShowAlert] = useState(false)
  const [alertMessage, setAlertMessage] = useState('')

  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    address: user?.address || '',
    city: user?.city || '',
    postalCode: user?.postalCode || ''
  })

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'الملف الشخصي', current: true }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    // TODO: Implement API call to update user profile
    console.log('Saving profile:', formData)

    setIsEditing(false)
    setAlertMessage('تم حفظ التغييرات بنجاح!')
    setShowAlert(true)
    setTimeout(() => setShowAlert(false), 3000)
  }

  const handleCancel = () => {
    setFormData({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || '',
      city: user?.city || '',
      postalCode: user?.postalCode || ''
    })
    setIsEditing(false)
  }

  if (!user) {
    return null
  }

  const userInitials = `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase()

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      {showAlert && (
        <Alert variant="success" dismissible onDismiss={() => setShowAlert(false)}>
          {alertMessage}
        </Alert>
      )}

      <ProfileHeader>
        <Avatar>
          {userInitials || <User size={32} />}
        </Avatar>
        <UserInfo>
          <UserName>{`${user.firstName} ${user.lastName}`}</UserName>
          <UserEmail>{user.email}</UserEmail>
        </UserInfo>
      </ProfileHeader>

      <ProfileSection padding="lg">
        <SectionHeader>
          <SectionTitle>
            <User size={20} />
            المعلومات الشخصية
          </SectionTitle>
          {!isEditing ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Edit2 size={16} />
              تعديل
            </Button>
          ) : (
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button
                variant="primary"
                size="sm"
                onClick={handleSave}
              >
                <Save size={16} />
                حفظ
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
              >
                <X size={16} />
                إلغاء
              </Button>
            </div>
          )}
        </SectionHeader>

        <FormGrid>
          <FormField>
            <Label>الاسم الأول</Label>
            {isEditing ? (
              <Input
                type="text"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                placeholder="الاسم الأول"
              />
            ) : (
              <ReadOnlyValue>{formData.firstName || 'غير محدد'}</ReadOnlyValue>
            )}
          </FormField>

          <FormField>
            <Label>الاسم الأخير</Label>
            {isEditing ? (
              <Input
                type="text"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                placeholder="الاسم الأخير"
              />
            ) : (
              <ReadOnlyValue>{formData.lastName || 'غير محدد'}</ReadOnlyValue>
            )}
          </FormField>

          <FormField>
            <Label>البريد الإلكتروني</Label>
            <ReadOnlyValue>{formData.email}</ReadOnlyValue>
          </FormField>

          <FormField>
            <Label>رقم الهاتف</Label>
            {isEditing ? (
              <Input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="رقم الهاتف"
              />
            ) : (
              <ReadOnlyValue>{formData.phone || 'غير محدد'}</ReadOnlyValue>
            )}
          </FormField>
        </FormGrid>
      </ProfileSection>

      <ProfileSection padding="lg">
        <SectionHeader>
          <SectionTitle>
            <MapPin size={20} />
            معلومات العنوان
          </SectionTitle>
        </SectionHeader>

        <FormGrid>
          <FormField style={{ gridColumn: '1 / -1' }}>
            <Label>العنوان</Label>
            {isEditing ? (
              <Input
                type="text"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="العنوان الكامل"
              />
            ) : (
              <ReadOnlyValue>{formData.address || 'غير محدد'}</ReadOnlyValue>
            )}
          </FormField>

          <FormField>
            <Label>المدينة</Label>
            {isEditing ? (
              <Input
                type="text"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="المدينة"
              />
            ) : (
              <ReadOnlyValue>{formData.city || 'غير محدد'}</ReadOnlyValue>
            )}
          </FormField>

          <FormField>
            <Label>الرمز البريدي</Label>
            {isEditing ? (
              <Input
                type="text"
                value={formData.postalCode}
                onChange={(e) => handleInputChange('postalCode', e.target.value)}
                placeholder="الرمز البريدي"
              />
            ) : (
              <ReadOnlyValue>{formData.postalCode || 'غير محدد'}</ReadOnlyValue>
            )}
          </FormField>
        </FormGrid>
      </ProfileSection>
    </Container>
  )
}

export default ProfilePage
