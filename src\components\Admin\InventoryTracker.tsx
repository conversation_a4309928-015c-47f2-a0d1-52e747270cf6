import React, { useState } from 'react'
import styled from 'styled-components'
import { Package, AlertTriangle, TrendingUp, TrendingDown, Plus, Minus, Search } from 'lucide-react'
import { formatPrice } from '../../utils/formatters'
import Button from '../UI/Button'
import Input from '../UI/Input'
import Select from '../UI/Select'
import Card from '../UI/Card'
import Badge from '../UI/Badge'
import Modal from '../UI/Modal'
import type { Product } from '../../types'

interface InventoryItem extends Product {
  lowStockThreshold: number
  lastRestocked: string
  totalSold: number
}

interface StockMovement {
  id: string
  productId: string
  type: 'in' | 'out' | 'adjustment'
  quantity: number
  reason: string
  date: string
  user: string
}

interface InventoryTrackerProps {
  products: InventoryItem[]
  stockMovements: StockMovement[]
  onUpdateStock: (productId: string, newStock: number, reason: string) => void
  onUpdateThreshold: (productId: string, threshold: number) => void
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const StatCard = styled(Card)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`

const StatIcon = styled.div<{ $variant: 'primary' | 'warning' | 'error' | 'success' }>`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme, $variant }) => {
    switch ($variant) {
      case 'warning': return theme.colors.status.warning + '20'
      case 'error': return theme.colors.status.error + '20'
      case 'success': return theme.colors.status.success + '20'
      default: return theme.colors.primary + '20'
    }
  }};
  color: ${({ theme, $variant }) => {
    switch ($variant) {
      case 'warning': return theme.colors.status.warning
      case 'error': return theme.colors.status.error
      case 'success': return theme.colors.status.success
      default: return theme.colors.primary
    }
  }};
`

const StatInfo = styled.div`
  flex: 1;
`

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const FiltersSection = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    flex-direction: column;
  }
`

const SearchWrapper = styled.div`
  flex: 1;
  position: relative;
`

const SearchIcon = styled(Search)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${({ theme }) => theme.colors.text.light};
  width: 18px;
  height: 18px;
  pointer-events: none;
`

const InventoryTable = styled.div`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  overflow: hidden;
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 60px 1fr 100px 120px 120px 100px 150px;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.surface};
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: none;
  }
`

const InventoryRow = styled.div`
  display: grid;
  grid-template-columns: 60px 1fr 100px 120px 120px 100px 150px;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  align-items: center;
  transition: background-color ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    display: block;
    padding: ${({ theme }) => theme.spacing.lg};
  }
`

const ProductImage = styled.img`
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ProductName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ProductCategory = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const StockCell = styled.div<{ $status: 'normal' | 'low' | 'out' }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme, $status }) => {
    switch ($status) {
      case 'out': return theme.colors.status.error
      case 'low': return theme.colors.status.warning
      default: return theme.colors.text.primary
    }
  }};
`

const StockActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  align-items: center;
`

const StockButton = styled.button`
  width: 28px;
  height: 28px;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const ThresholdInput = styled(Input)`
  width: 80px;
  text-align: center;
`

const StockModal = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
`

const ModalTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;
`

const ModalForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const Label = styled.label`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const ModalActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${({ theme }) => theme.spacing.lg};
`

const InventoryTracker: React.FC<InventoryTrackerProps> = ({
  products,
  stockMovements,
  onUpdateStock,
  onUpdateThreshold
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [showStockModal, setShowStockModal] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<InventoryItem | null>(null)
  const [stockAdjustment, setStockAdjustment] = useState({
    quantity: 0,
    reason: ''
  })

  // Calculate statistics
  const totalProducts = products.length
  const lowStockProducts = products.filter(p => (p.stock || 0) <= p.lowStockThreshold).length
  const outOfStockProducts = products.filter(p => (p.stock || 0) === 0).length
  const totalValue = products.reduce((sum, p) => sum + ((p.stock || 0) * p.price), 0)

  // Filter products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesStatus = true
    if (filterStatus === 'low') {
      matchesStatus = (product.stock || 0) <= product.lowStockThreshold && (product.stock || 0) > 0
    } else if (filterStatus === 'out') {
      matchesStatus = (product.stock || 0) === 0
    } else if (filterStatus === 'normal') {
      matchesStatus = (product.stock || 0) > product.lowStockThreshold
    }
    
    return matchesSearch && matchesStatus
  })

  const getStockStatus = (stock: number, threshold: number) => {
    if (stock === 0) return 'out'
    if (stock <= threshold) return 'low'
    return 'normal'
  }

  const getStockBadge = (stock: number, threshold: number) => {
    const status = getStockStatus(stock, threshold)
    switch (status) {
      case 'out':
        return <Badge variant="error" size="sm">نفد المخزون</Badge>
      case 'low':
        return <Badge variant="warning" size="sm">مخزون منخفض</Badge>
      default:
        return <Badge variant="success" size="sm">متوفر</Badge>
    }
  }

  const handleQuickAdjustment = (product: InventoryItem, change: number) => {
    const newStock = Math.max(0, (product.stock || 0) + change)
    const reason = change > 0 ? 'تعديل سريع - زيادة' : 'تعديل سريع - نقصان'
    onUpdateStock(product.id, newStock, reason)
  }

  const handleOpenStockModal = (product: InventoryItem) => {
    setSelectedProduct(product)
    setStockAdjustment({ quantity: 0, reason: '' })
    setShowStockModal(true)
  }

  const handleStockUpdate = () => {
    if (selectedProduct && stockAdjustment.reason.trim()) {
      const newStock = Math.max(0, (selectedProduct.stock || 0) + stockAdjustment.quantity)
      onUpdateStock(selectedProduct.id, newStock, stockAdjustment.reason)
      setShowStockModal(false)
      setSelectedProduct(null)
    }
  }

  const handleThresholdUpdate = (productId: string, threshold: number) => {
    onUpdateThreshold(productId, Math.max(0, threshold))
  }

  const statusOptions = [
    { value: '', label: 'جميع المنتجات' },
    { value: 'normal', label: 'مخزون طبيعي' },
    { value: 'low', label: 'مخزون منخفض' },
    { value: 'out', label: 'نفد المخزون' }
  ]

  return (
    <Container>
      <StatsGrid>
        <StatCard padding="md">
          <StatIcon $variant="primary">
            <Package size={24} />
          </StatIcon>
          <StatInfo>
            <StatValue>{totalProducts}</StatValue>
            <StatLabel>إجمالي المنتجات</StatLabel>
          </StatInfo>
        </StatCard>

        <StatCard padding="md">
          <StatIcon $variant="warning">
            <AlertTriangle size={24} />
          </StatIcon>
          <StatInfo>
            <StatValue>{lowStockProducts}</StatValue>
            <StatLabel>مخزون منخفض</StatLabel>
          </StatInfo>
        </StatCard>

        <StatCard padding="md">
          <StatIcon $variant="error">
            <TrendingDown size={24} />
          </StatIcon>
          <StatInfo>
            <StatValue>{outOfStockProducts}</StatValue>
            <StatLabel>نفد المخزون</StatLabel>
          </StatInfo>
        </StatCard>

        <StatCard padding="md">
          <StatIcon $variant="success">
            <TrendingUp size={24} />
          </StatIcon>
          <StatInfo>
            <StatValue>{formatPrice(totalValue)}</StatValue>
            <StatLabel>قيمة المخزون</StatLabel>
          </StatInfo>
        </StatCard>
      </StatsGrid>

      <FiltersSection>
        <SearchWrapper>
          <Input
            type="text"
            placeholder="البحث في المنتجات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />
          <SearchIcon />
        </SearchWrapper>
        
        <Select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          options={statusOptions}
        />
      </FiltersSection>

      <InventoryTable>
        <TableHeader>
          <div>الصورة</div>
          <div>المنتج</div>
          <div>المخزون</div>
          <div>الحد الأدنى</div>
          <div>القيمة</div>
          <div>الحالة</div>
          <div>الإجراءات</div>
        </TableHeader>

        {filteredProducts.map((product) => (
          <InventoryRow key={product.id}>
            <ProductImage src={product.images[0]} alt={product.name} />
            <ProductInfo>
              <ProductName>{product.name}</ProductName>
              <ProductCategory>{product.category}</ProductCategory>
            </ProductInfo>
            <StockCell $status={getStockStatus(product.stock || 0, product.lowStockThreshold)}>
              {product.stock || 0}
            </StockCell>
            <ThresholdInput
              type="number"
              value={product.lowStockThreshold}
              onChange={(e) => handleThresholdUpdate(product.id, parseInt(e.target.value) || 0)}
              min="0"
            />
            <div>{formatPrice((product.stock || 0) * product.price)}</div>
            <div>{getStockBadge(product.stock || 0, product.lowStockThreshold)}</div>
            <StockActions>
              <StockButton
                onClick={() => handleQuickAdjustment(product, -1)}
                disabled={(product.stock || 0) === 0}
                title="تقليل بواحد"
              >
                <Minus size={14} />
              </StockButton>
              <StockButton
                onClick={() => handleQuickAdjustment(product, 1)}
                title="زيادة بواحد"
              >
                <Plus size={14} />
              </StockButton>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleOpenStockModal(product)}
              >
                تعديل
              </Button>
            </StockActions>
          </InventoryRow>
        ))}
      </InventoryTable>

      <Modal
        isOpen={showStockModal}
        onClose={() => setShowStockModal(false)}
        size="sm"
      >
        <StockModal>
          <ModalTitle>تعديل المخزون - {selectedProduct?.name}</ModalTitle>
          <ModalForm>
            <FormField>
              <Label>المخزون الحالي</Label>
              <Input
                type="number"
                value={selectedProduct?.stock || 0}
                disabled
              />
            </FormField>
            
            <FormField>
              <Label>التغيير (+ للزيادة، - للنقصان)</Label>
              <Input
                type="number"
                value={stockAdjustment.quantity}
                onChange={(e) => setStockAdjustment(prev => ({
                  ...prev,
                  quantity: parseInt(e.target.value) || 0
                }))}
                placeholder="0"
              />
            </FormField>
            
            <FormField>
              <Label>المخزون الجديد</Label>
              <Input
                type="number"
                value={Math.max(0, (selectedProduct?.stock || 0) + stockAdjustment.quantity)}
                disabled
              />
            </FormField>
            
            <FormField>
              <Label>سبب التعديل *</Label>
              <Input
                type="text"
                value={stockAdjustment.reason}
                onChange={(e) => setStockAdjustment(prev => ({
                  ...prev,
                  reason: e.target.value
                }))}
                placeholder="أدخل سبب التعديل"
              />
            </FormField>
          </ModalForm>
          
          <ModalActions>
            <Button
              variant="primary"
              onClick={handleStockUpdate}
              disabled={!stockAdjustment.reason.trim()}
            >
              تحديث المخزون
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowStockModal(false)}
            >
              إلغاء
            </Button>
          </ModalActions>
        </StockModal>
      </Modal>
    </Container>
  )
}

export default InventoryTracker
