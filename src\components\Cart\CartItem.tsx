import React from 'react'
import styled from 'styled-components'
import { Minus, Plus, Trash2 } from 'lucide-react'
import { useCart } from '../../context/CartContext'
import { formatPrice } from '../../utils/formatters'
import Button from '../UI/Button'
import type { CartItem as CartItemType } from '../../types'

interface CartItemProps {
  item: CartItemType
}

const ItemContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background-color: ${({ theme }) => theme.colors.background};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.sm};
  }
`

const ProductImage = styled.img`
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  flex-shrink: 0;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    width: 100%;
    height: 200px;
  }
`

const ItemDetails = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ProductName = styled.h3`
  font-size: 1rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  line-height: 1.4;
`

const ProductVariants = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const VariantItem = styled.span`
  &:not(:last-child)::after {
    content: '•';
    margin-right: ${({ theme }) => theme.spacing.xs};
    margin-left: ${({ theme }) => theme.spacing.xs};
  }
`

const PriceSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: ${({ theme }) => theme.spacing.xs};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    align-items: flex-start;
  }
`

const Price = styled.div`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.primary};
`

const QuantityControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  margin-top: ${({ theme }) => theme.spacing.sm};
`

const QuantityButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text.primary};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.colors.surface};
    border-color: ${({ theme }) => theme.colors.primary};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }
`

const QuantityDisplay = styled.span`
  min-width: 40px;
  text-align: center;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const RemoveButton = styled(Button)`
  margin-top: ${({ theme }) => theme.spacing.sm};
`

const StockWarning = styled.div`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.status.warning};
  margin-top: ${({ theme }) => theme.spacing.xs};
`

const CartItem: React.FC<CartItemProps> = ({ item }) => {
  const { updateQuantity, removeFromCart } = useCart()

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity > 0) {
      updateQuantity(item.id, newQuantity)
    }
  }

  const handleRemove = () => {
    removeFromCart(item.id)
  }

  const isLowStock = item.quantity >= (item.stock || 0)
  const totalPrice = item.price * item.quantity

  return (
    <ItemContainer>
      <ProductImage 
        src={item.image} 
        alt={item.name}
        loading="lazy"
      />
      
      <ItemDetails>
        <ProductName>{item.name}</ProductName>
        
        {(item.size || item.color) && (
          <ProductVariants>
            {item.size && <VariantItem>المقاس: {item.size}</VariantItem>}
            {item.color && <VariantItem>اللون: {item.color}</VariantItem>}
          </ProductVariants>
        )}
        
        <QuantityControls>
          <QuantityButton
            onClick={() => handleQuantityChange(item.quantity - 1)}
            disabled={item.quantity <= 1}
            aria-label="تقليل الكمية"
          >
            <Minus size={16} />
          </QuantityButton>
          
          <QuantityDisplay>{item.quantity}</QuantityDisplay>
          
          <QuantityButton
            onClick={() => handleQuantityChange(item.quantity + 1)}
            disabled={isLowStock}
            aria-label="زيادة الكمية"
          >
            <Plus size={16} />
          </QuantityButton>
        </QuantityControls>
        
        {isLowStock && (
          <StockWarning>
            الكمية المتاحة: {item.stock || 0}
          </StockWarning>
        )}
        
        <RemoveButton
          variant="ghost"
          size="sm"
          onClick={handleRemove}
          aria-label={`إزالة ${item.name} من السلة`}
        >
          <Trash2 size={16} />
          إزالة من السلة
        </RemoveButton>
      </ItemDetails>
      
      <PriceSection>
        <Price>{formatPrice(totalPrice)}</Price>
      </PriceSection>
    </ItemContainer>
  )
}

export default CartItem
