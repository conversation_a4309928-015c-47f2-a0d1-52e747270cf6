import React, { useState } from 'react'
import styled from 'styled-components'
import {
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Package,
  TrendingUp,
  DollarSign,
  Hash,
  Image,
  MoreVertical
} from 'lucide-react'
import { formatPrice, formatDate } from '../../utils/formatters'
import Card from '../../components/UI/Card'
import Button from '../../components/UI/Button'
import Input from '../../components/UI/Input'
import Select from '../../components/UI/Select'
import Badge from '../../components/UI/Badge'
import Modal from '../../components/UI/Modal'

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    justify-content: stretch;
  }
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const StatCard = styled(Card)`
  text-align: center;
`

const StatIcon = styled.div<{ $color: string }>`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ $color }) => $color}20;
  color: ${({ $color }) => $color};
  margin: 0 auto ${({ theme }) => theme.spacing.md};
`

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const FiltersSection = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr auto;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: end;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`

const CategoriesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
`

const CategoryCard = styled(Card)`
  position: relative;
  transition: all ${({ theme }) => theme.transitions.medium};

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }
`

const CategoryImage = styled.div`
  width: 100%;
  height: 150px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
  }
`

const CategoryInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
`

const CategoryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`

const CategoryName = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const CategoryActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ActionButton = styled(Button)`
  padding: ${({ theme }) => theme.spacing.xs};
  min-width: auto;
`

const CategoryStats = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`

const CategoryStat = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const CategoryStatValue = styled.div`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const CategoryStatLabel = styled.div`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

// Mock data
const mockCategoryStats = {
  totalCategories: 12,
  activeCategories: 10,
  totalProducts: 156,
  totalRevenue: 125430
}

const mockCategories = [
  {
    id: '1',
    name: 'قمصان',
    description: 'مجموعة متنوعة من القمصان الرجالية والنسائية',
    productCount: 45,
    revenue: 32450,
    status: 'active',
    createdAt: '2023-12-01',
    image: null
  },
  {
    id: '2',
    name: 'فساتين',
    description: 'فساتين أنيقة لجميع المناسبات',
    productCount: 32,
    revenue: 28900,
    status: 'active',
    createdAt: '2023-11-15',
    image: null
  },
  {
    id: '3',
    name: 'بناطيل',
    description: 'بناطيل كاجوال ورسمية',
    productCount: 28,
    revenue: 21750,
    status: 'active',
    createdAt: '2023-10-20',
    image: null
  },
  {
    id: '4',
    name: 'جاكيتات',
    description: 'جاكيتات شتوية وصيفية',
    productCount: 18,
    revenue: 15600,
    status: 'active',
    createdAt: '2023-09-10',
    image: null
  },
  {
    id: '5',
    name: 'تنانير',
    description: 'تنانير كلاسيكية وعصرية',
    productCount: 15,
    revenue: 12300,
    status: 'active',
    createdAt: '2023-08-25',
    image: null
  },
  {
    id: '6',
    name: 'إكسسوارات',
    description: 'إكسسوارات متنوعة لإكمال الإطلالة',
    productCount: 8,
    revenue: 5400,
    status: 'inactive',
    createdAt: '2023-07-15',
    image: null
  }
]

const AdminCategories: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<any>(null)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success" size="sm">نشط</Badge>
      case 'inactive':
        return <Badge variant="secondary" size="sm">غير نشط</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  const filteredCategories = mockCategories.filter(category => {
    const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || category.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleViewCategory = (category: any) => {
    setSelectedCategory(category)
  }

  return (
    <Container>
      <Header>
        <Title>إدارة الفئات</Title>
        <HeaderActions>
          <Button variant="primary" onClick={() => setShowAddModal(true)}>
            <Plus size={16} />
            إضافة فئة جديدة
          </Button>
        </HeaderActions>
      </Header>

      <StatsGrid>
        <StatCard padding="lg">
          <StatIcon $color="#3B82F6">
            <Hash size={24} />
          </StatIcon>
          <StatValue>{mockCategoryStats.totalCategories}</StatValue>
          <StatLabel>إجمالي الفئات</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#10B981">
            <TrendingUp size={24} />
          </StatIcon>
          <StatValue>{mockCategoryStats.activeCategories}</StatValue>
          <StatLabel>فئات نشطة</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#F59E0B">
            <Package size={24} />
          </StatIcon>
          <StatValue>{mockCategoryStats.totalProducts}</StatValue>
          <StatLabel>إجمالي المنتجات</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#8B5CF6">
            <DollarSign size={24} />
          </StatIcon>
          <StatValue>{formatPrice(mockCategoryStats.totalRevenue)} ر.س</StatValue>
          <StatLabel>إجمالي الإيرادات</StatLabel>
        </StatCard>
      </StatsGrid>

      <FiltersSection padding="lg">
        <FiltersGrid>
          <Input
            type="text"
            placeholder="البحث في الفئات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<Search size={16} />}
          />

          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع الحالات' },
              { value: 'active', label: 'نشط' },
              { value: 'inactive', label: 'غير نشط' }
            ]}
          />

          <Button variant="outline">
            <Filter size={16} />
            تصفية متقدمة
          </Button>
        </FiltersGrid>
      </FiltersSection>

      <CategoriesGrid>
        {filteredCategories.map((category) => (
          <CategoryCard key={category.id} padding="lg">
            <CategoryImage>
              <Image size={48} style={{ position: 'relative', zIndex: 1 }} />
            </CategoryImage>

            <CategoryInfo>
              <CategoryHeader>
                <div>
                  <CategoryName>{category.name}</CategoryName>
                  {getStatusBadge(category.status)}
                </div>
                <CategoryActions>
                  <ActionButton variant="outline" size="sm" onClick={() => handleViewCategory(category)}>
                    <Eye size={14} />
                  </ActionButton>
                  <ActionButton variant="outline" size="sm">
                    <Edit size={14} />
                  </ActionButton>
                  <ActionButton variant="outline" size="sm">
                    <MoreVertical size={14} />
                  </ActionButton>
                </CategoryActions>
              </CategoryHeader>

              <p style={{
                fontSize: '0.875rem',
                color: '#6B7280',
                margin: '0 0 1rem 0',
                lineHeight: '1.5'
              }}>
                {category.description}
              </p>

              <CategoryStats>
                <CategoryStat>
                  <CategoryStatValue>{category.productCount}</CategoryStatValue>
                  <CategoryStatLabel>منتج</CategoryStatLabel>
                </CategoryStat>
                <CategoryStat>
                  <CategoryStatValue>{formatPrice(category.revenue)} ر.س</CategoryStatValue>
                  <CategoryStatLabel>الإيرادات</CategoryStatLabel>
                </CategoryStat>
              </CategoryStats>

              <div style={{
                fontSize: '0.75rem',
                color: '#9CA3AF',
                marginTop: '1rem',
                textAlign: 'center'
              }}>
                تم الإنشاء في {formatDate(category.createdAt)}
              </div>
            </CategoryInfo>
          </CategoryCard>
        ))}
      </CategoriesGrid>

      {showAddModal && (
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="إضافة فئة جديدة"
        >
          <div style={{ padding: '1rem' }}>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                اسم الفئة
              </label>
              <Input
                type="text"
                placeholder="أدخل اسم الفئة..."
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                الوصف
              </label>
              <textarea
                placeholder="أدخل وصف الفئة..."
                style={{
                  width: '100%',
                  minHeight: '80px',
                  padding: '0.75rem',
                  border: '1px solid #D1D5DB',
                  borderRadius: '8px',
                  fontSize: '0.875rem',
                  resize: 'vertical'
                }}
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                الحالة
              </label>
              <Select
                value="active"
                onChange={() => {}}
                options={[
                  { value: 'active', label: 'نشط' },
                  { value: 'inactive', label: 'غير نشط' }
                ]}
              />
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', marginTop: '2rem' }}>
              <Button variant="outline" onClick={() => setShowAddModal(false)}>
                إلغاء
              </Button>
              <Button variant="primary">
                حفظ الفئة
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </Container>
  )
}

export default AdminCategories
