import React, { useState } from 'react'
import styled from 'styled-components'
import { Package, Calendar, CreditCard, Eye, Download } from 'lucide-react'
import { formatPrice, formatDate } from '../../utils/formatters'
import Breadcrumb from '../../components/UI/Breadcrumb'
import Button from '../../components/UI/Button'
import Card from '../../components/UI/Card'
import Badge from '../../components/UI/Badge'
import Pagination from '../../components/UI/Pagination'

const Container = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const BreadcrumbWrapper = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const OrdersCount = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`

const OrderCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  transition: all ${({ theme }) => theme.transitions.fast};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
`

const OrderHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.md};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.sm};
  }
`

const OrderInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const OrderNumber = styled.h3`
  font-size: 1.125rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`

const OrderMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${({ theme }) => theme.spacing.xs};
  }
`

const MetaItem = styled.span`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
`

const OrderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    width: 100%;
    justify-content: stretch;
  }
`

const OrderItems = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const OrderItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ItemImage = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`

const ItemInfo = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ItemName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
`

const ItemDetails = styled.span`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const ItemPrice = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
`

const OrderSummary = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
`

const TotalPrice = styled.span`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.primary};
`

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  color: ${({ theme }) => theme.colors.text.secondary};
`

const EmptyIcon = styled.div`
  font-size: 4rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  opacity: 0.5;
`

const EmptyTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`

// Mock data for demonstration
const mockOrders = [
  {
    id: 'ORD-001',
    date: '2024-01-15',
    status: 'delivered',
    total: 299.99,
    items: [
      {
        id: '1',
        name: 'قميص قطني أزرق',
        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400',
        quantity: 2,
        price: 149.99,
        size: 'L',
        color: 'أزرق'
      }
    ]
  },
  {
    id: 'ORD-002',
    date: '2024-01-10',
    status: 'shipped',
    total: 199.99,
    items: [
      {
        id: '2',
        name: 'بنطال جينز أسود',
        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400',
        quantity: 1,
        price: 199.99,
        size: '32',
        color: 'أسود'
      }
    ]
  }
]

const ITEMS_PER_PAGE = 5

const OrderHistoryPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)

  const breadcrumbItems = [
    { label: 'الرئيسية', href: '/' },
    { label: 'تاريخ الطلبات', current: true }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning" size="sm">قيد المعالجة</Badge>
      case 'confirmed':
        return <Badge variant="info" size="sm">مؤكد</Badge>
      case 'shipped':
        return <Badge variant="primary" size="sm">تم الشحن</Badge>
      case 'delivered':
        return <Badge variant="success" size="sm">تم التسليم</Badge>
      case 'cancelled':
        return <Badge variant="error" size="sm">ملغي</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  const totalPages = Math.ceil(mockOrders.length / ITEMS_PER_PAGE)
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const paginatedOrders = mockOrders.slice(startIndex, startIndex + ITEMS_PER_PAGE)

  return (
    <Container>
      <BreadcrumbWrapper>
        <Breadcrumb items={breadcrumbItems} />
      </BreadcrumbWrapper>

      <PageHeader>
        <div>
          <Title>تاريخ الطلبات</Title>
          <OrdersCount>
            {mockOrders.length} {mockOrders.length === 1 ? 'طلب' : 'طلبات'}
          </OrdersCount>
        </div>
      </PageHeader>

      {mockOrders.length === 0 ? (
        <EmptyState>
          <EmptyIcon>📦</EmptyIcon>
          <EmptyTitle>لا توجد طلبات</EmptyTitle>
          <p>لم تقم بأي طلبات حتى الآن. ابدأ التسوق الآن!</p>
          <Button variant="primary" onClick={() => window.location.href = '/products'}>
            تصفح المنتجات
          </Button>
        </EmptyState>
      ) : (
        <>
          {paginatedOrders.map((order) => (
            <OrderCard key={order.id} padding="lg">
              <OrderHeader>
                <OrderInfo>
                  <OrderNumber>
                    <Package size={20} />
                    طلب رقم {order.id}
                  </OrderNumber>
                  <OrderMeta>
                    <MetaItem>
                      <Calendar size={16} />
                      {formatDate(order.date)}
                    </MetaItem>
                    <MetaItem>
                      <CreditCard size={16} />
                      {formatPrice(order.total)}
                    </MetaItem>
                  </OrderMeta>
                </OrderInfo>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', alignItems: 'flex-end' }}>
                  {getStatusBadge(order.status)}
                  <OrderActions>
                    <Button variant="outline" size="sm">
                      <Eye size={16} />
                      عرض التفاصيل
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download size={16} />
                      تحميل الفاتورة
                    </Button>
                  </OrderActions>
                </div>
              </OrderHeader>

              <OrderItems>
                {order.items.map((item) => (
                  <OrderItem key={item.id}>
                    <ItemImage src={item.image} alt={item.name} />
                    <ItemInfo>
                      <ItemName>{item.name}</ItemName>
                      <ItemDetails>
                        الكمية: {item.quantity} | المقاس: {item.size} | اللون: {item.color}
                      </ItemDetails>
                    </ItemInfo>
                    <ItemPrice>{formatPrice(item.price)}</ItemPrice>
                  </OrderItem>
                ))}
              </OrderItems>

              <OrderSummary>
                <span>المجموع الكلي:</span>
                <TotalPrice>{formatPrice(order.total)}</TotalPrice>
              </OrderSummary>
            </OrderCard>
          ))}

          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          )}
        </>
      )}
    </Container>
  )
}

export default OrderHistoryPage
