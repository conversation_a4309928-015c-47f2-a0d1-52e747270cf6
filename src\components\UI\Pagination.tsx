import React from 'react'
import styled from 'styled-components'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showFirstLast?: boolean
  maxVisiblePages?: number
  className?: string
}

const PaginationContainer = styled.nav`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.xs};
`

const PaginationList = styled.ul`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  list-style: none;
  margin: 0;
  padding: 0;
`

const PaginationItem = styled.li``

const PaginationButton = styled.button<{ $active?: boolean; $disabled?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: ${({ theme }) => theme.spacing.sm};
  border: 1px solid ${({ theme }) => theme.colors.border.medium};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ $active, theme }) => 
    $active ? theme.colors.primary : theme.colors.background};
  color: ${({ $active, theme }) => 
    $active ? theme.colors.text.white : theme.colors.text.primary};
  font-family: ${({ theme }) => theme.fonts.primary};
  font-size: 0.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover:not(:disabled) {
    background-color: ${({ $active, theme }) => 
      $active ? theme.colors.accent : theme.colors.surface};
    border-color: ${({ theme }) => theme.colors.primary};
  }
  
  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const EllipsisSpan = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  color: ${({ theme }) => theme.colors.text.light};
`

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  maxVisiblePages = 5,
  className
}) => {
  const getVisiblePages = () => {
    const pages: (number | 'ellipsis')[] = []
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      const halfVisible = Math.floor(maxVisiblePages / 2)
      let startPage = Math.max(1, currentPage - halfVisible)
      let endPage = Math.min(totalPages, currentPage + halfVisible)
      
      // Adjust if we're near the beginning or end
      if (currentPage <= halfVisible) {
        endPage = maxVisiblePages
      } else if (currentPage >= totalPages - halfVisible) {
        startPage = totalPages - maxVisiblePages + 1
      }
      
      // Add first page and ellipsis if needed
      if (startPage > 1) {
        pages.push(1)
        if (startPage > 2) {
          pages.push('ellipsis')
        }
      }
      
      // Add visible pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i)
      }
      
      // Add ellipsis and last page if needed
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('ellipsis')
        }
        pages.push(totalPages)
      }
    }
    
    return pages
  }

  const visiblePages = getVisiblePages()

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page)
    }
  }

  if (totalPages <= 1) {
    return null
  }

  return (
    <PaginationContainer className={className} aria-label="تنقل بين الصفحات">
      <PaginationList>
        {/* First page button */}
        {showFirstLast && currentPage > 1 && (
          <PaginationItem>
            <PaginationButton
              onClick={() => handlePageChange(1)}
              aria-label="الصفحة الأولى"
            >
              الأولى
            </PaginationButton>
          </PaginationItem>
        )}
        
        {/* Previous page button */}
        <PaginationItem>
          <PaginationButton
            onClick={() => handlePageChange(currentPage - 1)}
            $disabled={currentPage === 1}
            disabled={currentPage === 1}
            aria-label="الصفحة السابقة"
          >
            <ChevronRight size={18} />
          </PaginationButton>
        </PaginationItem>
        
        {/* Page numbers */}
        {visiblePages.map((page, index) => (
          <PaginationItem key={index}>
            {page === 'ellipsis' ? (
              <EllipsisSpan>
                <MoreHorizontal size={18} />
              </EllipsisSpan>
            ) : (
              <PaginationButton
                onClick={() => handlePageChange(page)}
                $active={page === currentPage}
                aria-label={`الصفحة ${page}`}
                aria-current={page === currentPage ? 'page' : undefined}
              >
                {page}
              </PaginationButton>
            )}
          </PaginationItem>
        ))}
        
        {/* Next page button */}
        <PaginationItem>
          <PaginationButton
            onClick={() => handlePageChange(currentPage + 1)}
            $disabled={currentPage === totalPages}
            disabled={currentPage === totalPages}
            aria-label="الصفحة التالية"
          >
            <ChevronLeft size={18} />
          </PaginationButton>
        </PaginationItem>
        
        {/* Last page button */}
        {showFirstLast && currentPage < totalPages && (
          <PaginationItem>
            <PaginationButton
              onClick={() => handlePageChange(totalPages)}
              aria-label="الصفحة الأخيرة"
            >
              الأخيرة
            </PaginationButton>
          </PaginationItem>
        )}
      </PaginationList>
    </PaginationContainer>
  )
}

export default Pagination
