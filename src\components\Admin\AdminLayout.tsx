import React, { useState } from 'react'
import { Outlet } from 'react-router-dom'
import styled from 'styled-components'
import { Menu, X, Bell, Search, User } from 'lucide-react'
import AdminSidebar from './AdminSidebar'
import { useAuth } from '../../context/AuthContext'

const LayoutContainer = styled.div`
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
`

const MainContent = styled.div<{ $sidebarOpen: boolean }>`
  flex: 1;
  margin-right: ${props => props.$sidebarOpen ? '280px' : '0'};
  transition: margin-right 0.3s ease;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    margin-right: ${props => props.$sidebarOpen ? '260px' : '0'};
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    margin-right: 0;
  }
`

const TopBar = styled.header`
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
`

const TopBarLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`

const MenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing.sm};
  border-radius: 8px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text.secondary};
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors.surface};
    color: ${({ theme }) => theme.colors.text.primary};
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    display: flex;
  }
`

const PageTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  font-family: 'Cairo', sans-serif;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    font-size: 1.25rem;
  }
`

const TopBarRight = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
`

const SearchContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    display: none;
  }
`

const SearchInput = styled.input`
  width: 300px;
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  padding-right: 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: #f9fafb;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.text.primary};
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`

const SearchIcon = styled.div`
  position: absolute;
  right: 12px;
  color: #9ca3af;
  pointer-events: none;
`

const IconButton = styled.button`
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing.sm};
  border-radius: 8px;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.text.secondary};
  position: relative;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors.surface};
    color: ${({ theme }) => theme.colors.text.primary};
  }
`

const NotificationBadge = styled.span`
  position: absolute;
  top: 4px;
  left: 4px;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  border: 2px solid white;
`

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors.surface};
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    display: none;
  }
`

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #000 0%, #333 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
`

const UserName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const ContentArea = styled.main`
  padding: ${({ theme }) => theme.spacing.xl};
  min-height: calc(100vh - 70px);
  
  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    padding: ${({ theme }) => theme.spacing.lg} ${({ theme }) => theme.spacing.md};
  }
`

const MobileOverlay = styled.div<{ $visible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: ${props => props.$visible ? 1 : 0};
  visibility: ${props => props.$visible ? 'visible' : 'hidden'};
  transition: all 0.3s ease;
  
  @media (min-width: ${({ theme }) => theme.breakpoints.mobile}) {
    display: none;
  }
`

const AdminLayout: React.FC = () => {
  const [sidebarOpen] = useState(true)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { user } = useAuth()



  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  const closeMobileMenu = () => {
    setMobileMenuOpen(false)
  }

  const getPageTitle = () => {
    const path = window.location.pathname
    if (path === '/admin') return 'لوحة التحكم'
    if (path.includes('/products')) return 'إدارة المنتجات'
    if (path.includes('/orders')) return 'إدارة الطلبات'
    if (path.includes('/customers')) return 'إدارة العملاء'
    if (path.includes('/categories')) return 'إدارة الفئات'
    if (path.includes('/discounts')) return 'إدارة الخصومات'
    if (path.includes('/analytics')) return 'التقارير والتحليلات'
    return 'لوحة الإدارة'
  }

  return (
    <LayoutContainer>
      <AdminSidebar 
        isOpen={sidebarOpen || mobileMenuOpen} 
        onClose={closeMobileMenu}
      />
      
      <MainContent $sidebarOpen={sidebarOpen}>
        <TopBar>
          <TopBarLeft>
            <MenuButton onClick={toggleMobileMenu}>
              {mobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
            </MenuButton>
            <PageTitle>{getPageTitle()}</PageTitle>
          </TopBarLeft>

          <TopBarRight>
            <SearchContainer>
              <SearchInput 
                type="text" 
                placeholder="البحث في لوحة الإدارة..."
              />
              <SearchIcon>
                <Search size={16} />
              </SearchIcon>
            </SearchContainer>

            <IconButton>
              <Bell size={20} />
              <NotificationBadge />
            </IconButton>

            <UserInfo>
              <UserAvatar>
                <User size={16} />
              </UserAvatar>
              <UserName>{user?.name || 'مدير المتجر'}</UserName>
            </UserInfo>
          </TopBarRight>
        </TopBar>

        <ContentArea>
          <Outlet />
        </ContentArea>
      </MainContent>

      <MobileOverlay 
        $visible={mobileMenuOpen} 
        onClick={closeMobileMenu}
      />
    </LayoutContainer>
  )
}

export default AdminLayout
