import React, { useState } from 'react'
import styled from 'styled-components'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  Download,
  Filter,
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { formatPrice, formatPercentage, formatDate } from '../../utils/formatters'
import Card from '../../components/UI/Card'
import Button from '../../components/UI/Button'
import Select from '../../components/UI/Select'

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    justify-content: stretch;
  }
`

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const MetricCard = styled(Card)`
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors.primary};
  }
`

const MetricHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`

const MetricIcon = styled.div<{ $color: string }>`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ $color }) => $color}20;
  color: ${({ $color }) => $color};
`

const MetricValue = styled.div`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const MetricLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`

const MetricChange = styled.div<{ $positive: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  font-size: 0.875rem;
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme, $positive }) =>
    $positive ? theme.colors.status.success : theme.colors.status.error};
`

const ChartsSection = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`

const ChartCard = styled(Card)`
  min-height: 400px;
`

const ChartHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`

const ChartTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: ${({ theme }) => theme.fonts.weights.semibold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`

const ChartPlaceholder = styled.div`
  height: 300px;
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.surface} 0%, ${({ theme }) => theme.colors.background} 100%);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  border: 2px dashed ${({ theme }) => theme.colors.border.light};
  text-align: center;
  gap: ${({ theme }) => theme.spacing.md};
`

const AdminAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30d')

  // Mock analytics data
  const mockAnalytics = {
    revenue: { value: 125430, change: 12.5, positive: true },
    orders: { value: 1247, change: -3.2, positive: false },
    customers: { value: 892, change: 15.3, positive: true },
    avgOrderValue: { value: 285.50, change: 5.7, positive: true }
  }

  return (
    <Container>
      <Header>
        <Title>التقارير والإحصائيات</Title>
        <HeaderActions>
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            options={[
              { value: '7d', label: 'آخر 7 أيام' },
              { value: '30d', label: 'آخر 30 يوم' },
              { value: '90d', label: 'آخر 3 أشهر' },
              { value: '1y', label: 'آخر سنة' }
            ]}
          />
          <Button variant="outline">
            <Filter size={16} />
            تصفية
          </Button>
          <Button variant="outline">
            <Download size={16} />
            تصدير التقرير
          </Button>
        </HeaderActions>
      </Header>

      <MetricsGrid>
        <MetricCard padding="lg">
          <MetricHeader>
            <MetricIcon $color="#10B981">
              <DollarSign size={24} />
            </MetricIcon>
            <MetricChange $positive={mockAnalytics.revenue.positive}>
              {mockAnalytics.revenue.positive ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
              {formatPercentage(mockAnalytics.revenue.change)}%
            </MetricChange>
          </MetricHeader>
          <MetricValue>{formatPrice(mockAnalytics.revenue.value)} ر.س</MetricValue>
          <MetricLabel>إجمالي الإيرادات</MetricLabel>
        </MetricCard>

        <MetricCard padding="lg">
          <MetricHeader>
            <MetricIcon $color="#3B82F6">
              <ShoppingCart size={24} />
            </MetricIcon>
            <MetricChange $positive={mockAnalytics.orders.positive}>
              {mockAnalytics.orders.positive ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
              {formatPercentage(Math.abs(mockAnalytics.orders.change))}%
            </MetricChange>
          </MetricHeader>
          <MetricValue>{mockAnalytics.orders.value}</MetricValue>
          <MetricLabel>إجمالي الطلبات</MetricLabel>
        </MetricCard>

        <MetricCard padding="lg">
          <MetricHeader>
            <MetricIcon $color="#8B5CF6">
              <Users size={24} />
            </MetricIcon>
            <MetricChange $positive={mockAnalytics.customers.positive}>
              {mockAnalytics.customers.positive ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
              {formatPercentage(mockAnalytics.customers.change)}%
            </MetricChange>
          </MetricHeader>
          <MetricValue>{mockAnalytics.customers.value}</MetricValue>
          <MetricLabel>إجمالي العملاء</MetricLabel>
        </MetricCard>

        <MetricCard padding="lg">
          <MetricHeader>
            <MetricIcon $color="#F59E0B">
              <TrendingUp size={24} />
            </MetricIcon>
            <MetricChange $positive={mockAnalytics.avgOrderValue.positive}>
              {mockAnalytics.avgOrderValue.positive ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
              {formatPercentage(mockAnalytics.avgOrderValue.change)}%
            </MetricChange>
          </MetricHeader>
          <MetricValue>{formatPrice(mockAnalytics.avgOrderValue.value)} ر.س</MetricValue>
          <MetricLabel>متوسط قيمة الطلب</MetricLabel>
        </MetricCard>
      </MetricsGrid>

      <ChartsSection>
        <ChartCard padding="lg">
          <ChartHeader>
            <ChartTitle>
              <LineChart size={20} />
              الإيرادات الشهرية
            </ChartTitle>
            <Select
              value="revenue"
              onChange={() => {}}
              options={[
                { value: 'revenue', label: 'الإيرادات' },
                { value: 'orders', label: 'الطلبات' },
                { value: 'customers', label: 'العملاء' }
              ]}
            />
          </ChartHeader>
          <ChartPlaceholder>
            <LineChart size={48} />
            <div>مخطط الإيرادات الشهرية</div>
            <div style={{ fontSize: '0.75rem', opacity: 0.7 }}>
              سيتم عرض بيانات الإيرادات والمبيعات هنا
            </div>
          </ChartPlaceholder>
        </ChartCard>

        <ChartCard padding="lg">
          <ChartHeader>
            <ChartTitle>
              <PieChart size={20} />
              توزيع الفئات
            </ChartTitle>
          </ChartHeader>
          <ChartPlaceholder>
            <PieChart size={48} />
            <div>توزيع المبيعات حسب الفئة</div>
            <div style={{ fontSize: '0.75rem', opacity: 0.7 }}>
              سيتم عرض توزيع المبيعات حسب فئات المنتجات
            </div>
          </ChartPlaceholder>
        </ChartCard>
      </ChartsSection>

      <ChartsSection>
        <ChartCard padding="lg">
          <ChartHeader>
            <ChartTitle>
              <BarChart3 size={20} />
              أفضل المنتجات
            </ChartTitle>
            <Button variant="outline" size="sm">
              عرض الكل
            </Button>
          </ChartHeader>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {[
              { name: 'قميص قطني أزرق', revenue: 12450, change: 15.2 },
              { name: 'فستان صيفي أبيض', revenue: 9870, change: 8.5 },
              { name: 'بنطلون جينز أسود', revenue: 8650, change: -2.1 },
              { name: 'جاكيت شتوي رمادي', revenue: 7320, change: 22.3 },
              { name: 'تنورة كلاسيكية', revenue: 6540, change: 5.8 }
            ].map((product, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '1rem',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
              }}>
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{product.name}</div>
                  <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>
                    {formatPrice(product.revenue)} ر.س
                  </div>
                </div>
                <div style={{
                  color: product.change > 0 ? '#10B981' : '#EF4444',
                  fontSize: '0.875rem',
                  fontWeight: 'bold'
                }}>
                  {product.change > 0 ? '+' : ''}{product.change}%
                </div>
              </div>
            ))}
          </div>
        </ChartCard>

        <ChartCard padding="lg">
          <ChartHeader>
            <ChartTitle>
              <Package size={20} />
              أفضل الفئات
            </ChartTitle>
            <Button variant="outline" size="sm">
              عرض الكل
            </Button>
          </ChartHeader>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {[
              { name: 'قمصان', revenue: 45230, change: 12.8 },
              { name: 'فساتين', revenue: 38950, change: 18.2 },
              { name: 'بناطيل', revenue: 32100, change: -5.3 },
              { name: 'جاكيتات', revenue: 28750, change: 25.1 },
              { name: 'تنانير', revenue: 22400, change: 8.9 }
            ].map((category, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '1rem',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
              }}>
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{category.name}</div>
                  <div style={{ fontSize: '0.875rem', color: '#6B7280' }}>
                    {formatPrice(category.revenue)} ر.س
                  </div>
                </div>
                <div style={{
                  color: category.change > 0 ? '#10B981' : '#EF4444',
                  fontSize: '0.875rem',
                  fontWeight: 'bold'
                }}>
                  {category.change > 0 ? '+' : ''}{category.change}%
                </div>
              </div>
            ))}
          </div>
        </ChartCard>
      </ChartsSection>
    </Container>
  )
}

export default AdminAnalytics
