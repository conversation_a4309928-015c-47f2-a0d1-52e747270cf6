import React, { useState } from 'react'
import styled from 'styled-components'
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Package,
  TrendingUp,
  AlertTriangle,
  MoreVertical
} from 'lucide-react'
import { formatPrice } from '../../utils/formatters'
import Card from '../../components/UI/Card'
import Button from '../../components/UI/Button'
import Input from '../../components/UI/Input'
import Select from '../../components/UI/Select'
import Badge from '../../components/UI/Badge'
import Modal from '../../components/UI/Modal'
import ResponsiveTable from '../../components/UI/ResponsiveTable'

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.lg};
  min-height: 60vh;
`

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.xl};

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.md};
    align-items: stretch;
  }
`

const Title = styled.h1`
  font-size: 2rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin: 0;
`

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakpoints.mobile}) {
    justify-content: stretch;
  }
`

const FiltersSection = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: end;

  @media (max-width: ${({ theme }) => theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
  }
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`

const StatCard = styled(Card)`
  text-align: center;
`

const StatIcon = styled.div<{ $color: string }>`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ $color }) => $color}20;
  color: ${({ $color }) => $color};
  margin: 0 auto ${({ theme }) => theme.spacing.md};
`

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: ${({ theme }) => theme.fonts.weights.bold};
  color: ${({ theme }) => theme.colors.text.primary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`



const ProductImage = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: ${({ theme }) => theme.borderRadius.md};
`

const ProductInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`

const ProductName = styled.span`
  font-weight: ${({ theme }) => theme.fonts.weights.medium};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
`

const ProductSKU = styled.span`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`

const ProductActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.xs};
  justify-content: flex-end;
`

const ActionButton = styled(Button)`
  padding: ${({ theme }) => theme.spacing.xs};
  min-width: auto;
`

// Mock data
const mockProductStats = {
  totalProducts: 156,
  activeProducts: 142,
  lowStock: 8,
  outOfStock: 6
}

const mockProducts = [
  {
    id: '1',
    name: 'قميص قطني أزرق',
    sku: 'SHIRT-001',
    category: 'قمصان',
    price: 149.99,
    stock: 25,
    status: 'active',
    image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400'
  },
  {
    id: '2',
    name: 'فستان صيفي أبيض',
    sku: 'DRESS-002',
    category: 'فساتين',
    price: 299.99,
    stock: 12,
    status: 'active',
    image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400'
  },
  {
    id: '3',
    name: 'بنطلون جينز أسود',
    sku: 'JEANS-003',
    category: 'بناطيل',
    price: 199.99,
    stock: 0,
    status: 'out_of_stock',
    image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=400'
  },
  {
    id: '4',
    name: 'جاكيت شتوي رمادي',
    sku: 'JACKET-004',
    category: 'جاكيتات',
    price: 399.99,
    stock: 5,
    status: 'low_stock',
    image: 'https://images.unsplash.com/photo-1551028719-00167b16eac5?w=400'
  },
  {
    id: '5',
    name: 'تنورة كلاسيكية',
    sku: 'SKIRT-005',
    category: 'تنانير',
    price: 179.99,
    stock: 18,
    status: 'active',
    image: 'https://images.unsplash.com/photo-1583496661160-fb5886a13d77?w=400'
  }
]

const AdminProducts: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success" size="sm">نشط</Badge>
      case 'inactive':
        return <Badge variant="secondary" size="sm">غير نشط</Badge>
      case 'low_stock':
        return <Badge variant="warning" size="sm">مخزون منخفض</Badge>
      case 'out_of_stock':
        return <Badge variant="error" size="sm">نفد المخزون</Badge>
      default:
        return <Badge variant="secondary" size="sm">{status}</Badge>
    }
  }

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !categoryFilter || product.category === categoryFilter
    const matchesStatus = !statusFilter || product.status === statusFilter

    return matchesSearch && matchesCategory && matchesStatus
  })

  return (
    <Container>
      <Header>
        <Title>إدارة المنتجات</Title>
        <HeaderActions>
          <Button variant="primary" onClick={() => setShowAddModal(true)}>
            <Plus size={16} />
            إضافة منتج جديد
          </Button>
        </HeaderActions>
      </Header>

      <StatsGrid>
        <StatCard padding="lg">
          <StatIcon $color="#3B82F6">
            <Package size={24} />
          </StatIcon>
          <StatValue>{mockProductStats.totalProducts}</StatValue>
          <StatLabel>إجمالي المنتجات</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#10B981">
            <TrendingUp size={24} />
          </StatIcon>
          <StatValue>{mockProductStats.activeProducts}</StatValue>
          <StatLabel>المنتجات النشطة</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#F59E0B">
            <AlertTriangle size={24} />
          </StatIcon>
          <StatValue>{mockProductStats.lowStock}</StatValue>
          <StatLabel>مخزون منخفض</StatLabel>
        </StatCard>

        <StatCard padding="lg">
          <StatIcon $color="#EF4444">
            <AlertTriangle size={24} />
          </StatIcon>
          <StatValue>{mockProductStats.outOfStock}</StatValue>
          <StatLabel>نفد المخزون</StatLabel>
        </StatCard>
      </StatsGrid>

      <FiltersSection padding="lg">
        <FiltersGrid>
          <Input
            type="text"
            placeholder="البحث في المنتجات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={<Search size={16} />}
          />

          <Select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع الفئات' },
              { value: 'قمصان', label: 'قمصان' },
              { value: 'فساتين', label: 'فساتين' },
              { value: 'بناطيل', label: 'بناطيل' },
              { value: 'جاكيتات', label: 'جاكيتات' },
              { value: 'تنانير', label: 'تنانير' }
            ]}
          />

          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: '', label: 'جميع الحالات' },
              { value: 'active', label: 'نشط' },
              { value: 'inactive', label: 'غير نشط' },
              { value: 'low_stock', label: 'مخزون منخفض' },
              { value: 'out_of_stock', label: 'نفد المخزون' }
            ]}
          />

          <Button variant="outline">
            <Filter size={16} />
            تصفية متقدمة
          </Button>
        </FiltersGrid>
      </FiltersSection>

      <ResponsiveTable
        title={`المنتجات (${filteredProducts.length})`}
        actions={
          <Button variant="outline" size="sm">
            تصدير البيانات
          </Button>
        }
        columns={[
          {
            key: 'image',
            label: 'الصورة',
            hideOnMobile: true,
            render: (value, row) => (
              <ProductImage src={row.image} alt={row.name} />
            )
          },
          {
            key: 'product',
            label: 'المنتج',
            render: (value, row) => (
              <ProductInfo>
                <ProductName>{row.name}</ProductName>
                <ProductSKU>SKU: {row.sku}</ProductSKU>
              </ProductInfo>
            )
          },
          {
            key: 'category',
            label: 'الفئة',
            hideOnMobile: true
          },
          {
            key: 'price',
            label: 'السعر',
            render: (value, row) => `${formatPrice(row.price)} ر.س`
          },
          {
            key: 'stock',
            label: 'المخزون'
          },
          {
            key: 'status',
            label: 'الحالة',
            render: (value, row) => getStatusBadge(row.status)
          },
          {
            key: 'actions',
            label: 'الإجراءات',
            render: (value, row) => (
              <ProductActions>
                <ActionButton variant="outline" size="sm">
                  <Eye size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Edit size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Trash2 size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <MoreVertical size={14} />
                </ActionButton>
              </ProductActions>
            )
          }
        ]}
        data={filteredProducts}
        keyField="id"
        mobileCardRender={(product) => (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <div style={{ display: 'flex', gap: '1rem', alignItems: 'flex-start' }}>
              <ProductImage src={product.image} alt={product.name} />
              <div style={{ flex: 1 }}>
                <ProductInfo>
                  <ProductName>{product.name}</ProductName>
                  <ProductSKU>SKU: {product.sku}</ProductSKU>
                </ProductInfo>
                <div style={{ marginTop: '0.5rem', fontSize: '0.875rem', color: '#6B7280' }}>
                  {product.category}
                </div>
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', fontSize: '0.875rem' }}>
              <div>
                <span style={{ fontWeight: 'bold' }}>السعر: </span>
                {formatPrice(product.price)} ر.س
              </div>
              <div>
                <span style={{ fontWeight: 'bold' }}>المخزون: </span>
                {product.stock}
              </div>
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              {getStatusBadge(product.status)}
              <ProductActions>
                <ActionButton variant="outline" size="sm">
                  <Eye size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <Edit size={14} />
                </ActionButton>
                <ActionButton variant="outline" size="sm">
                  <MoreVertical size={14} />
                </ActionButton>
              </ProductActions>
            </div>
          </div>
        )}
      />

      {showAddModal && (
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="إضافة منتج جديد"
        >
          <div style={{ padding: '1rem' }}>
            <p>نموذج إضافة منتج جديد سيتم تطويره هنا.</p>
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end', marginTop: '2rem' }}>
              <Button variant="outline" onClick={() => setShowAddModal(false)}>
                إلغاء
              </Button>
              <Button variant="primary">
                حفظ المنتج
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </Container>
  )
}

export default AdminProducts
